"""
产品规格系统性能测试
测试重构后的系统性能表现
"""
import asyncio
import time
import statistics
from typing import List, Dict, Any
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.products.services.product_specification import ProductSpecificationService
from svc.apps.products.schemas.specs import SpecWithOptions
from tests.conftest import async_session_maker


class PerformanceTestSuite:
    """性能测试套件"""
    
    def __init__(self, service: ProductSpecificationService):
        self.service = service
        self.results = []
    
    async def measure_time(self, operation_name: str, operation, *args, **kwargs):
        """测量操作执行时间"""
        start_time = time.time()
        result = await operation(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        self.results.append({
            'operation': operation_name,
            'execution_time': execution_time,
            'success': result.is_success if hasattr(result, 'is_success') else True
        })
        
        print(f"{operation_name}: {execution_time:.4f}s")
        return result
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not self.results:
            return {}
        
        times = [r['execution_time'] for r in self.results]
        success_rate = sum(1 for r in self.results if r['success']) / len(self.results)
        
        return {
            'total_operations': len(self.results),
            'success_rate': success_rate,
            'avg_time': statistics.mean(times),
            'median_time': statistics.median(times),
            'min_time': min(times),
            'max_time': max(times),
            'std_dev': statistics.stdev(times) if len(times) > 1 else 0
        }


@pytest.mark.asyncio
async def test_specification_setup_performance():
    """测试规格设置性能"""
    async with async_session_maker() as session:
        # 初始化服务
        from svc.apps.products.dependencies import get_product_specification_service
        service = await get_product_specification_service()
        
        test_suite = PerformanceTestSuite(service)
        
        # 测试数据
        product_id = 1
        specs_data = [
            SpecWithOptions(name="颜色", options=["红色", "蓝色", "绿色", "黑色", "白色"]),
            SpecWithOptions(name="尺寸", options=["XS", "S", "M", "L", "XL", "XXL"]),
            SpecWithOptions(name="材质", options=["棉", "涤纶", "丝绸", "羊毛"])
        ]
        
        # 测试规格设置性能
        result = await test_suite.measure_time(
            "setup_specifications",
            service.setup_product_specifications,
            product_id,
            specs_data
        )
        
        assert result.is_success
        
        # 验证生成的组合数量 (5 * 6 * 4 = 120)
        combinations = result.data
        assert len(combinations) == 120
        
        print(f"Generated {len(combinations)} combinations")


@pytest.mark.asyncio
async def test_specification_query_performance():
    """测试规格查询性能"""
    async with async_session_maker() as session:
        from svc.apps.products.dependencies import get_product_specification_service
        service = await get_product_specification_service()
        
        test_suite = PerformanceTestSuite(service)
        product_id = 1
        
        # 测试多次查询性能（包括缓存效果）
        for i in range(10):
            await test_suite.measure_time(
                f"get_specifications_round_{i+1}",
                service.get_product_specifications,
                product_id
            )
        
        stats = test_suite.get_statistics()
        print(f"Query performance stats: {stats}")
        
        # 验证缓存效果：后续查询应该更快
        times = [r['execution_time'] for r in test_suite.results]
        first_query_time = times[0]
        avg_cached_time = statistics.mean(times[1:]) if len(times) > 1 else first_query_time
        
        print(f"First query: {first_query_time:.4f}s")
        print(f"Average cached query: {avg_cached_time:.4f}s")
        
        # 缓存查询应该比首次查询快（允许一定误差）
        assert avg_cached_time <= first_query_time * 1.1


@pytest.mark.asyncio
async def test_batch_pricing_update_performance():
    """测试批量价格更新性能"""
    async with async_session_maker() as session:
        from svc.apps.products.dependencies import get_product_specification_service
        service = await get_product_specification_service()
        
        test_suite = PerformanceTestSuite(service)
        product_id = 1
        
        # 先获取所有组合ID
        specs_result = await service.get_product_specifications(product_id)
        combination_ids = [combo.id for combo in specs_result.data.combinations]
        
        # 测试不同批次大小的性能
        batch_sizes = [10, 25, 50, 100]
        
        for batch_size in batch_sizes:
            if len(combination_ids) >= batch_size:
                batch_ids = combination_ids[:batch_size]
                pricing_data = {"price": 99.99, "cost_price": 60.00}
                
                await test_suite.measure_time(
                    f"batch_update_pricing_{batch_size}",
                    service.update_combination_pricing,
                    batch_ids,
                    pricing_data
                )
        
        stats = test_suite.get_statistics()
        print(f"Batch update performance: {stats}")


@pytest.mark.asyncio
async def test_search_performance():
    """测试搜索性能"""
    async with async_session_maker() as session:
        from svc.apps.products.dependencies import get_product_specification_service
        service = await get_product_specification_service()
        
        test_suite = PerformanceTestSuite(service)
        product_id = 1
        
        # 获取一些规格值ID用于搜索
        specs_result = await service.get_product_specifications(product_id)
        if specs_result.is_success and specs_result.data.specs:
            spec_option_ids = []
            for spec in specs_result.data.specs[:2]:  # 取前两个规格
                if spec.get('options'):
                    spec_option_ids.append(spec['options'][0]['id'])
            
            if spec_option_ids:
                # 测试按规格值搜索
                await test_suite.measure_time(
                    "search_by_specs",
                    service.find_combinations_by_specs,
                    product_id,
                    spec_option_ids
                )
        
        # 测试按价格范围搜索
        await test_suite.measure_time(
            "search_by_price_range",
            service.get_combinations_by_price_range,
            product_id,
            50.0,
            150.0
        )
        
        stats = test_suite.get_statistics()
        print(f"Search performance: {stats}")


@pytest.mark.asyncio
async def test_concurrent_operations():
    """测试并发操作性能"""
    async with async_session_maker() as session:
        from svc.apps.products.dependencies import get_product_specification_service
        service = await get_product_specification_service()
        
        # 创建多个并发查询任务
        tasks = []
        for i in range(10):
            task = service.get_product_specifications(1)
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time_per_request = total_time / len(tasks)
        
        print(f"Concurrent operations:")
        print(f"Total time: {total_time:.4f}s")
        print(f"Average time per request: {avg_time_per_request:.4f}s")
        print(f"Requests per second: {len(tasks) / total_time:.2f}")
        
        # 验证所有请求都成功
        assert all(result.is_success for result in results)


@pytest.mark.asyncio
async def test_memory_usage():
    """测试内存使用情况"""
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    async with async_session_maker() as session:
        from svc.apps.products.dependencies import get_product_specification_service
        service = await get_product_specification_service()
        
        # 执行大量操作
        for i in range(100):
            await service.get_product_specifications(1)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"Memory usage:")
        print(f"Initial: {initial_memory:.2f} MB")
        print(f"Final: {final_memory:.2f} MB")
        print(f"Increase: {memory_increase:.2f} MB")
        
        # 内存增长应该在合理范围内（小于100MB）
        assert memory_increase < 100


def run_performance_tests():
    """运行所有性能测试"""
    print("=" * 60)
    print("产品规格系统性能测试")
    print("=" * 60)
    
    tests = [
        test_specification_setup_performance,
        test_specification_query_performance,
        test_batch_pricing_update_performance,
        test_search_performance,
        test_concurrent_operations,
        test_memory_usage
    ]
    
    for test in tests:
        print(f"\n运行测试: {test.__name__}")
        print("-" * 40)
        try:
            asyncio.run(test())
            print("✅ 测试通过")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("性能测试完成")
    print("=" * 60)


if __name__ == "__main__":
    run_performance_tests()
