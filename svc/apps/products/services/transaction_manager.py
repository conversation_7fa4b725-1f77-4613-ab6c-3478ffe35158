"""
事务管理器
为产品规格服务提供事务管理和错误处理
"""
import functools
from typing import Any, Callable, TypeVar, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from svc.core.services.result import Result
from svc.core.exceptions.error_codes import ErrorCode
import logging

logger = logging.getLogger(__name__)

F = TypeVar('F', bound=Callable[..., Any])


def transactional(rollback_on_error: bool = True, isolation_level: Optional[str] = None):
    """
    事务装饰器
    
    Args:
        rollback_on_error: 出错时是否回滚事务
        isolation_level: 事务隔离级别
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            # 检查是否有数据库会话
            if not hasattr(self, 'spec_repo') or not self.spec_repo:
                logger.error(f"服务 {self.__class__.__name__} 缺少数据库仓库")
                return self.create_error_result(
                    ErrorCode.INTERNAL_ERROR, 
                    "服务配置错误：缺少数据库仓库"
                )
            
            db: AsyncSession = self.spec_repo.db
            
            try:
                # 开始事务
                async with db.begin():
                    if isolation_level:
                        await db.execute(f"SET TRANSACTION ISOLATION LEVEL {isolation_level}")
                    
                    result = await func(self, *args, **kwargs)
                    
                    # 如果返回的是错误结果且需要回滚，则抛出异常触发回滚
                    if (rollback_on_error and 
                        isinstance(result, Result) and 
                        not result.is_success):
                        raise TransactionRollbackException(result.error_message)
                    
                    return result
                    
            except TransactionRollbackException:
                # 重新抛出，让外层处理
                raise
            except IntegrityError as e:
                logger.error(f"数据完整性错误: {str(e)}", exc_info=True)
                return self.create_error_result(
                    ErrorCode.VALIDATION_ERROR,
                    "数据完整性约束违反，请检查输入数据"
                )
            except SQLAlchemyError as e:
                logger.error(f"数据库操作错误: {str(e)}", exc_info=True)
                return self.create_error_result(
                    ErrorCode.DATABASE_ERROR,
                    f"数据库操作失败: {str(e)}"
                )
            except Exception as e:
                logger.error(f"事务执行失败: {str(e)}", exc_info=True)
                return self.create_error_result(
                    ErrorCode.INTERNAL_ERROR,
                    f"操作失败: {str(e)}"
                )
        
        return wrapper
    return decorator


def batch_transactional(batch_size: int = 100, continue_on_error: bool = False):
    """
    批量事务装饰器
    
    Args:
        batch_size: 批处理大小
        continue_on_error: 出错时是否继续处理剩余批次
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(self, items, *args, **kwargs):
            if not items:
                return self.create_success_result([])
            
            results = []
            errors = []
            
            # 分批处理
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                
                try:
                    batch_result = await func(self, batch, *args, **kwargs)
                    
                    if isinstance(batch_result, Result):
                        if batch_result.is_success:
                            results.extend(batch_result.data if isinstance(batch_result.data, list) else [batch_result.data])
                        else:
                            errors.append({
                                'batch_index': i // batch_size,
                                'error': batch_result.error_message
                            })
                            if not continue_on_error:
                                break
                    else:
                        results.extend(batch_result if isinstance(batch_result, list) else [batch_result])
                        
                except Exception as e:
                    error_msg = f"批次 {i // batch_size} 处理失败: {str(e)}"
                    logger.error(error_msg, exc_info=True)
                    errors.append({
                        'batch_index': i // batch_size,
                        'error': error_msg
                    })
                    
                    if not continue_on_error:
                        break
            
            # 构建结果
            if errors and not continue_on_error:
                return self.create_error_result(
                    ErrorCode.BATCH_OPERATION_FAILED,
                    f"批量操作失败: {errors[0]['error']}"
                )
            elif errors and continue_on_error:
                return self.create_success_result({
                    'results': results,
                    'errors': errors,
                    'total_processed': len(results),
                    'total_errors': len(errors)
                })
            else:
                return self.create_success_result(results)
        
        return wrapper
    return decorator


def retry_on_deadlock(max_retries: int = 3, delay: float = 0.1):
    """
    死锁重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试延迟（秒）
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            import asyncio
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except SQLAlchemyError as e:
                    # 检查是否是死锁错误
                    if "deadlock" in str(e).lower() and attempt < max_retries:
                        logger.warning(f"检测到死锁，第 {attempt + 1} 次重试: {str(e)}")
                        await asyncio.sleep(delay * (2 ** attempt))  # 指数退避
                        continue
                    else:
                        raise
            
        return wrapper
    return decorator


class TransactionRollbackException(Exception):
    """事务回滚异常"""
    pass


class TransactionManager:
    """事务管理器类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def execute_in_transaction(
        self, 
        operations: list, 
        isolation_level: Optional[str] = None
    ) -> Result:
        """
        在事务中执行多个操作
        
        Args:
            operations: 操作列表，每个操作是一个可调用对象
            isolation_level: 事务隔离级别
        """
        try:
            async with self.db.begin():
                if isolation_level:
                    await self.db.execute(f"SET TRANSACTION ISOLATION LEVEL {isolation_level}")
                
                results = []
                for operation in operations:
                    if callable(operation):
                        result = await operation()
                        results.append(result)
                    else:
                        raise ValueError(f"操作必须是可调用对象: {operation}")
                
                return Result.success(results)
                
        except Exception as e:
            logger.error(f"事务执行失败: {str(e)}", exc_info=True)
            return Result.error(ErrorCode.INTERNAL_ERROR, f"事务执行失败: {str(e)}")
    
    async def execute_with_savepoint(self, operation: Callable, savepoint_name: str = "sp1") -> Result:
        """
        使用保存点执行操作
        
        Args:
            operation: 要执行的操作
            savepoint_name: 保存点名称
        """
        try:
            # 创建保存点
            savepoint = await self.db.begin_nested()
            
            try:
                result = await operation()
                await savepoint.commit()
                return Result.success(result)
            except Exception as e:
                await savepoint.rollback()
                logger.warning(f"操作失败，已回滚到保存点 {savepoint_name}: {str(e)}")
                return Result.error(ErrorCode.OPERATION_FAILED, f"操作失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"保存点操作失败: {str(e)}", exc_info=True)
            return Result.error(ErrorCode.INTERNAL_ERROR, f"保存点操作失败: {str(e)}")


# 便捷的事务装饰器实例
transaction = transactional()
read_committed_transaction = transactional(isolation_level="READ COMMITTED")
serializable_transaction = transactional(isolation_level="SERIALIZABLE")
batch_transaction = batch_transactional()
retry_transaction = retry_on_deadlock()
