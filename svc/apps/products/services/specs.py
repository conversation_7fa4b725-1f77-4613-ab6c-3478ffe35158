from itertools import product
from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch

from svc.apps.albums.schemas.album import AlbumCreate
from svc.apps.albums.services.album import AlbumService
from svc.apps.products.models.specs import (ProductSpecCombination, Spec,
                                            SpecOption)
# ProductSpecificationRepository功能已合并到ProductSpecCombinationRepository
from svc.apps.products.repositories.specs import (
    ProductSpecCombinationRepository, SpecOptionRepository, SpecRepository)
from svc.apps.products.schemas.specs import (
    ProductSpecCombinationCreate, ProductSpecCombinationListResponse,
    ProductSpecCombinationResponse, ProductSpecificationResponse, SpecCreate,
    SpecListResponse, SpecOptionCreate, SpecOptionListResponse,
    SpecOptionResponse, SpecResponse, SpecWithOptions)
from svc.core.database.transactions import with_transaction
from svc.core.events.event_names import (SYSTEM_AUDIT_LOG_RECORDED,
                                         SYSTEM_CACHE_INVALIDATION_REQUESTED)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.schemas.base import PageParams
from svc.core.services.base import BaseService, BatchUpdateMixin
from svc.core.services.result import Result, ResultFactory


class SpecService(BaseService[Spec, Result[SpecResponse]]):
    """
    规格服务类，提供规格的增删改查
    """
    def __init__(self, repo: SpecRepository):
        super().__init__()
        self.repo = repo

    async def create_spec(self, spec: SpecCreate) -> Result[SpecResponse]:
        """创建规格"""
        try:
            data = await self.repo.create(spec)
            return self.create_success_result(data)
        except Exception as e:
            self.logger.error(f"创建规格失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.CREATE_FAILED, f"创建规格失败: {str(e)}")

    async def get_spec(self, spec_id: int) -> Result[SpecResponse]:
        """获取规格"""
        try:
            data = await self.repo.get(spec_id)
            if not data:
                return self.create_error_result(ErrorCode.NOT_FOUND, f"规格不存在: {spec_id}")
            return self.create_success_result(data)
        except Exception as e:
            self.logger.error(f"获取规格失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.NOT_FOUND, f"获取规格失败: {str(e)}")

    async def update_spec(self, spec_id: int, spec: SpecCreate) -> Result[SpecResponse]:
        """更新规格"""
        try:
            data = await self.repo.update(spec_id, spec)
            if not data:
                return self.create_error_result(ErrorCode.NOT_FOUND, f"规格不存在: {spec_id}")
            return self.create_success_result(data)
        except Exception as e:
            self.logger.error(f"更新规格失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.UPDATE_FAILED, f"更新规格失败: {str(e)}")

    async def delete_spec(self, spec_id: int) -> Result[bool]:
        """删除规格"""
        try:
            ok = await self.repo.delete(spec_id)
            if not ok:
                return self.create_error_result(ErrorCode.NOT_FOUND, f"规格不存在: {spec_id}")
            return self.create_success_result(True)
        except Exception as e:
            self.logger.error(f"删除规格失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.DELETE_FAILED, f"删除规格失败: {str(e)}")

    async def list_specs(self, product_id: int, params: PageParams) -> Result[SpecListResponse]:
        """获取商品所有规格（数据库级分页，支持with_total）"""
        try:
            items, total = await self.repo.get_paginated(
                page=params.page_num,
                page_size=params.page_size,
                with_total=True,
                product_id=product_id
            )
            total = total or 0
            page_count = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0
            resp = SpecListResponse(
                items=items,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )
            return self.create_success_result(resp)
        except Exception as e:
            self.logger.error(f"获取规格列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"获取规格列表失败: {str(e)}")

class SpecOptionService(BaseService[SpecOption, Result[SpecOptionResponse]]):
    """
    规格值服务类，提供规格值的增删改查
    """
    def __init__(self, repo: SpecOptionRepository):
        super().__init__()
        self.repo = repo

    async def create_option(self, option: SpecOptionCreate) -> Result:
        try:
            data = await self.repo.create(option)
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"创建规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"创建规格值失败: {str(e)}")

    async def get_option(self, option_id: int) -> Result:
        try:
            data = await self.repo.get(option_id)
            if not data:
                return ResultFactory.error(404, f"规格值不存在: {option_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"获取规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"获取规格值失败: {str(e)}")

    async def update_option(self, option_id: int, option: SpecOptionCreate) -> Result:
        try:
            data = await self.repo.update(option_id, option)
            if not data:
                return ResultFactory.error(404, f"规格值不存在: {option_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"更新规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"更新规格值失败: {str(e)}")

    async def delete_option(self, option_id: int) -> Result:
        try:
            ok = await self.repo.delete(option_id)
            if not ok:
                return ResultFactory.error(404, f"规格值不存在: {option_id}")
            return ResultFactory.success(True)
        except Exception as e:
            self.logger.error(f"删除规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"删除规格值失败: {str(e)}")

    async def list_options(self, spec_id: int, params: PageParams) -> Result[SpecOptionListResponse]:
        """获取指定规格的所有规格值（数据库级分页，支持with_total）"""
        try:
            items, total = await self.repo.get_paginated(
                page=params.page_num,
                page_size=params.page_size,
                with_total=True,
                spec_id=spec_id
            )
            total = total or 0
            page_count = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0
            resp = SpecOptionListResponse(
                items=items,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )
            return self.create_success_result(resp)
        except Exception as e:
            self.logger.error(f"获取规格值列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"获取规格值列表失败: {str(e)}")

# 删除冗余的ProductSpecService - 功能已整合到ProductSpecCombinationService

# 删除冗余的ProductSpecOptionService - 功能已整合到ProductSpecCombinationService

class ProductSpecCombinationService(BaseService[ProductSpecCombination, Result[ProductSpecCombinationResponse]], BatchUpdateMixin):
    """
    商品规格组合（可售单元）服务类，包含组合生成和统一规格管理
    整合了产品规格的完整业务逻辑，提供一站式服务
    """
    resource_type = "product_specification"

    def __init__(
        self,
        repo: ProductSpecCombinationRepository,
        album_service: AlbumService,
        basic_spec_repo: Optional[SpecRepository] = None,
        spec_option_repo: Optional[SpecOptionRepository] = None
    ):
        super().__init__()
        self.repo = repo
        self.album_service = album_service
        # 统一规格管理相关仓库 - 现在使用repo作为主仓库
        self.basic_spec_repo = basic_spec_repo
        self.spec_option_repo = spec_option_repo

    async def create_combination(self, combination: ProductSpecCombinationCreate) -> Result:
        """创建商品规格组合，并为其关联一个新图册"""
        try:
            # 1. 创建图册
            album_create_data = AlbumCreate(name=f"SKU图册 - {combination.sku}", tags=["product", "sku"])
            album_result = await self.album_service.create_album(album_create_data)
            if not album_result.success:
                return ResultFactory.error(ErrorCode.CREATE_FAILED, f"创建SKU关联图册失败: {album_result.error_message}")
            
            created_album = album_result.data

            # 2. 创建规格组合并关联图册
            data = await self.repo.create_with_album(combination, created_album.id)
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"创建商品规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"创建商品规格组合失败: {str(e)}")

    async def get_combination(self, combination_id: int) -> Result:
        """
        获取单个规格组合
        """
        try:
            data = await self.repo.get(combination_id)
            if not data:
                return ResultFactory.error(404, f"商品规格组合不存在: {combination_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"获取商品规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"获取商品规格组合失败: {str(e)}")

    async def update_combination(self, combination_id: int, update_data: dict) -> Result:
        """
        更新规格组合
        """
        try:
            data = await self.repo.update(combination_id, update_data)
            if not data:
                return ResultFactory.error(404, f"规格组合不存在: {combination_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"更新规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"更新规格组合失败: {str(e)}")

    async def delete_combination(self, combination_id: int) -> Result:
        """删除商品规格组合，并同步删除关联的图册"""
        try:
            combination_to_delete = await self.repo.get_by_id(combination_id)
            if not combination_to_delete:
                return ResultFactory.error(ErrorCode.NOT_FOUND, f"商品规格组合不存在: {combination_id}")

            # 1. 逻辑删除关联的图册
            if combination_to_delete.album_id:
                await self.album_service.delete_album(combination_to_delete.album_id)

            # 2. 删除规格组合
            ok = await self.repo.delete(combination_to_delete)
            if not ok:
                 return ResultFactory.error(ErrorCode.DELETE_FAILED, "删除商品规格组合记录失败")

            return ResultFactory.success(True)
        except Exception as e:
            self.logger.error(f"删除商品规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.DELETE_FAILED, f"删除商品规格组合失败: {str(e)}")

    async def list_combinations(self, product_id: int, params: PageParams) -> Result[ProductSpecCombinationListResponse]:
        """获取指定商品的所有规格组合（数据库级分页，支持with_total）"""
        try:
            items, total = await self.repo.get_paginated(
                page=params.page_num,
                page_size=params.page_size,
                with_total=True,
                product_id=product_id
            )
            total = total or 0
            page_count = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0
            resp = ProductSpecCombinationListResponse(
                items=items,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )
            return self.create_success_result(resp)
        except Exception as e:
            self.logger.error(f"获取规格组合列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"获取规格组合列表失败: {str(e)}")

    def generate_combinations(self, spec_option_id_lists: List[List[int]]) -> Result[List[List[int]]]:
        """
        生成所有规格值ID的组合（笛卡尔积）
        """
        try:
            from itertools import product
            data = [list(comb) for comb in product(*spec_option_id_lists)]
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"生成规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"生成规格组合失败: {str(e)}")

    def _generate_sku(self, product_id: int, option_ids: List[int]) -> str:
        """
        生成唯一SKU
        """
        return f"{product_id}-{'-'.join(map(str, option_ids))}"

    async def generate_and_save_combinations(
        self,
        product_id: int,
        spec_service,
        spec_option_service
    ) -> Result:
        """
        生成并批量保存所有商品规格组合，返回新建的组合列表
        """
        try:
            # 1. 获取商品所有规格
            spec_result = await spec_service.list_specs(product_id)
            if not spec_result.success or not spec_result.data:
                return ResultFactory.error(404, f"未找到商品规格: {product_id}")
            specs = spec_result.data
            if not specs:
                return ResultFactory.error(400, "商品未配置任何规格")

            # 2. 获取每个规格的规格值ID列表
            spec_option_id_lists: List[List[int]] = []
            for spec in specs:
                option_result = await spec_option_service.list_options(spec.id)
                if not option_result.success or not option_result.data:
                    return ResultFactory.error(400, f"规格[{spec.name}]未配置规格值")
                option_ids = [opt.id for opt in option_result.data]
                if not option_ids:
                    return ResultFactory.error(400, f"规格[{spec.name}]未配置规格值")
                spec_option_id_lists.append(option_ids)

            # 3. 生成所有组合
            comb_result = self.generate_combinations(spec_option_id_lists)
            if not comb_result.success or not comb_result.data:
                return ResultFactory.error(400, "生成规格组合失败")
            combinations = comb_result.data
            if not combinations:
                return ResultFactory.error(400, "无可用规格组合")

            # 4. 检查SKU唯一性，组装批量插入对象
            exist_result = await self.repo.list_by_product(product_id)
            exist_skus = set([c.sku for c in exist_result]) if exist_result else set()
            new_combinations: List[ProductSpecCombinationCreate] = []
            for option_ids in combinations:
                sku = self._generate_sku(product_id, option_ids)
                if sku in exist_skus:
                    continue  # 跳过已存在SKU
                new_combinations.append(ProductSpecCombinationCreate(
                    product_id=product_id,
                    sku=sku,
                    price=None,
                    stock_quantity=0,
                    spec_option_ids=option_ids
                ))
            if not new_combinations:
                return ResultFactory.error(400, "所有组合已存在，无需生成")

            # 5. 批量保存
            objs = await self.repo.bulk_create(new_combinations)
            return ResultFactory.success(objs)
        except Exception as e:
            self.logger.error(f"生成并保存规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"生成并保存规格组合失败: {str(e)}")

    # ========== 统一规格管理功能 ==========

    @with_transaction(auto_commit=True)
    async def setup_product_specifications(
        self,
        product_id: int,
        specs_data: List[SpecWithOptions],
        user_id: Optional[int] = None,
        db=None
    ) -> Result[List[ProductSpecCombinationResponse]]:
        """一站式设置产品规格"""
        try:
            # 1. 验证产品存在
            if not await self._validate_product_exists(product_id):
                return self.create_error_result(ErrorCode.NOT_FOUND, "产品不存在")

            # 2. 清理现有规格组合
            await self.repo.delete_product_combinations(product_id)

            # 3. 创建或获取规格和规格值
            spec_options_map = await self._ensure_specs_and_options(specs_data)

            if not spec_options_map:
                return self.create_error_result(ErrorCode.VALIDATION_ERROR, "未找到有效的规格配置")

            # 4. 生成所有可能的组合
            combinations_data = await self._generate_all_combinations(
                product_id, spec_options_map
            )

            if not combinations_data:
                return self.create_error_result(ErrorCode.VALIDATION_ERROR, "无法生成有效的规格组合")

            # 5. 批量创建组合
            created_combinations = await self.repo.bulk_create_combinations(combinations_data)

            # 6. 触发事件
            await self._dispatch_specification_events(
                product_id, created_combinations, user_id, "created"
            )

            # 7. 构建响应
            responses = [
                await self._build_combination_response(combo)
                for combo in created_combinations
            ]

            self.logger.info(f"成功设置产品规格: product_id={product_id}, 组合数量={len(responses)}")
            return self.create_success_result(responses)

        except Exception as e:
            self.logger.error(f"设置产品规格失败: product_id={product_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"设置规格失败: {str(e)}")

    async def get_product_specifications(
        self,
        product_id: int,
        include_inactive: bool = False
    ) -> Result[ProductSpecificationResponse]:
        """获取产品完整规格信息"""
        try:
            # 1. 尝试从缓存获取
            if not include_inactive:  # 只缓存活跃数据
                cache_key = f"product_specs:{product_id}"
                cached_data = await self.get_cached_resource(cache_key, ProductSpecificationResponse)
                if cached_data:
                    return self.create_success_result(cached_data)

            # 2. 从数据库获取规格和选项
            specs_with_options = await self.repo.get_product_specs_with_options(product_id)

            # 3. 获取组合信息
            combinations = await self.repo.get_combinations_with_specs(
                product_id, include_inactive=include_inactive
            )

            # 4. 获取统计信息
            stats = await self.repo.get_combination_stats(product_id)

            # 5. 构建响应数据
            response_data = ProductSpecificationResponse(
                product_id=product_id,
                specs=specs_with_options,
                combinations=[
                    await self._build_combination_response(combo) for combo in combinations
                ],
                stats=stats
            )

            # 6. 缓存结果（只缓存活跃数据）
            if not include_inactive:
                cache_key = f"product_specs:{product_id}"
                await self.cache_resource(cache_key, response_data)

            return self.create_success_result(response_data)

        except Exception as e:
            self.logger.error(f"获取产品规格失败: product_id={product_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"获取规格失败: {str(e)}")

    async def update_combination_pricing(
        self,
        combination_ids: List[int],
        pricing_data: Dict[str, Any],
        user_id: Optional[int] = None
    ) -> Result[List[ProductSpecCombinationResponse]]:
        """批量更新组合价格"""
        # 使用通用批量更新方法
        return await self.batch_update_resources(
            resource_ids=combination_ids,
            update_data=pricing_data,
            repository=self.repo,
            resource_type="规格组合",
            event_prefix="product:combination",
            cache_key_generator=lambda combo_id: f"product_specs:combination:{combo_id}"
        )

    # ========== 辅助方法 ==========

    async def _validate_product_exists(self, product_id: int) -> bool:
        """验证产品是否存在"""
        from sqlalchemy import select

        from svc.apps.products.models.product import Product

        query = select(Product).where(Product.id == product_id)
        result = await self.repo.db.execute(query)
        return result.scalars().first() is not None

    async def _ensure_specs_and_options(self, specs_data: List[SpecWithOptions]) -> Dict[int, List[int]]:
        """确保规格和规格值存在，返回规格ID到选项ID列表的映射"""
        if not self.basic_spec_repo or not self.spec_option_repo:
            return {}

        spec_options_map = {}

        for spec_data in specs_data:
            # 创建或获取规格
            spec = await self._get_or_create_spec(spec_data)
            if not spec:
                continue

            # 创建或获取规格值
            option_ids = []
            for option_value in spec_data.options:
                option = await self._get_or_create_spec_option(spec.id, option_value)
                if option:
                    option_ids.append(option.id)

            if option_ids:
                spec_options_map[spec.id] = option_ids

        return spec_options_map

    async def _get_or_create_spec(self, spec_data: SpecWithOptions) -> Optional[Spec]:
        """获取或创建规格"""
        if not self.basic_spec_repo:
            return None

        # 先尝试根据名称查找
        existing_spec = await self.basic_spec_repo.get_one(name=spec_data.name)
        if existing_spec:
            return existing_spec

        # 创建新规格
        spec_create = SpecCreate(
            name=spec_data.name,
            description=spec_data.description,
            sort_order=spec_data.sort_order
        )
        return await self.basic_spec_repo.create(spec_create)

    async def _get_or_create_spec_option(self, spec_id: int, value: str) -> Optional[SpecOption]:
        """获取或创建规格值"""
        if not self.spec_option_repo:
            return None

        # 先尝试查找
        existing_option = await self.spec_option_repo.get_one(spec_id=spec_id, value=value)
        if existing_option:
            return existing_option

        # 创建新规格值
        option_create = SpecOptionCreate(spec_id=spec_id, value=value)
        return await self.spec_option_repo.create(option_create)

    async def _generate_all_combinations(self, product_id: int, spec_options_map: Dict[int, List[int]]) -> List[Dict]:
        """生成所有规格组合"""
        # 获取所有规格的选项ID列表
        option_lists = list(spec_options_map.values())
        if not option_lists:
            return []

        combinations = []
        for option_combination in product(*option_lists):
            sku = await self.repo.generate_sku(product_id, list(option_combination))
            combinations.append({
                'product_id': product_id,
                'sku': sku,
                'spec_option_ids': list(option_combination),
                'stock_quantity': 0,
                'is_active': True
            })

        return combinations

    async def _build_combination_response(self, combo: ProductSpecCombination) -> ProductSpecCombinationResponse:
        """构建组合响应对象"""
        combo_dict = {
            'id': combo.id,
            'product_id': combo.product_id,
            'sku': combo.sku,
            'price': combo.price / 100 if combo.price else None,  # 转换为元
            'cost_price': combo.cost_price / 100 if combo.cost_price else None,
            'market_price': combo.market_price / 100 if combo.market_price else None,
            'stock_quantity': combo.stock_quantity,
            'min_stock_level': combo.min_stock_level,
            'max_stock_level': combo.max_stock_level,
            'is_active': combo.is_active,
            'is_default': combo.is_default,
            'weight': combo.weight,
            'barcode': combo.barcode,
            'description': combo.description,
            'spec_option_ids': [opt.id for opt in combo.spec_options] if combo.spec_options else [],
            'album_id': combo.album_id,
            'album': combo.album.to_dict() if combo.album else None
        }
        return ProductSpecCombinationResponse(**combo_dict)

    async def _dispatch_specification_events(
        self,
        product_id: int,
        combinations: List[ProductSpecCombination],
        user_id: Optional[int] = None,
        action: str = "updated"
    ):
        """触发规格相关事件"""
        try:
            # 触发缓存失效事件
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={
                "resource_type": "product",
                "resource_id": product_id
            })

            # 直接失效本地缓存
            cache_key = f"product_specs:{product_id}"
            await self.invalidate_cache(cache_key)

            # 触发审计日志事件
            if user_id:
                dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                    "user_id": user_id,
                    "action": f"product_specifications_{action}",
                    "resource_type": self.resource_type,
                    "resource_id": product_id,
                    "metadata": {
                        "combination_count": len(combinations),
                        "combination_ids": [c.id for c in combinations]
                    }
                })

        except Exception as e:
            self.logger.warning(f"触发规格事件失败: {str(e)}")