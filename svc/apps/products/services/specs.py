from itertools import product
from typing import List, Optional, cast

from sqlalchemy.exc import SQLAlchemyError

from svc.apps.albums.schemas.album import AlbumCreate
from svc.apps.albums.services.album import AlbumService
from svc.apps.products.models.specs import (ProductSpec,
                                            ProductSpecCombination,
                                            ProductSpecOption, Spec,
                                            SpecOption)
from svc.apps.products.repositories.specs import (
    ProductSpecCombinationRepository, ProductSpecOptionRepository,
    SpecOptionRepository, SpecRepository)
from svc.apps.products.schemas.specs import (
    ProductSpecCombinationCreate, ProductSpecCombinationListResponse,
    ProductSpecCombinationResponse, ProductSpecCreate, ProductSpecOptionCreate,
    ProductSpecOptionResponse, ProductSpecResponse, SpecCreate,
    SpecListResponse, SpecOptionCreate, SpecOptionListResponse,
    SpecOptionResponse, SpecResponse)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.schemas.base import PageParams
from svc.core.services.base import BaseService
from svc.core.services.result import Result, ResultFactory


class SpecService(BaseService[Spec, Result[SpecResponse]]):
    """
    规格服务类，提供规格的增删改查
    """
    def __init__(self, repo: SpecRepository):
        super().__init__()
        self.repo = repo

    async def create_spec(self, spec: SpecCreate) -> Result[SpecResponse]:
        """创建规格"""
        try:
            data = await self.repo.create(spec)
            return self.create_success_result(data)
        except Exception as e:
            self.logger.error(f"创建规格失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.CREATE_FAILED, f"创建规格失败: {str(e)}")

    async def get_spec(self, spec_id: int) -> Result[SpecResponse]:
        """获取规格"""
        try:
            data = await self.repo.get(spec_id)
            if not data:
                return self.create_error_result(ErrorCode.NOT_FOUND, f"规格不存在: {spec_id}")
            return self.create_success_result(data)
        except Exception as e:
            self.logger.error(f"获取规格失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.NOT_FOUND, f"获取规格失败: {str(e)}")

    async def update_spec(self, spec_id: int, spec: SpecCreate) -> Result[SpecResponse]:
        """更新规格"""
        try:
            data = await self.repo.update(spec_id, spec)
            if not data:
                return self.create_error_result(ErrorCode.NOT_FOUND, f"规格不存在: {spec_id}")
            return self.create_success_result(data)
        except Exception as e:
            self.logger.error(f"更新规格失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.UPDATE_FAILED, f"更新规格失败: {str(e)}")

    async def delete_spec(self, spec_id: int) -> Result[bool]:
        """删除规格"""
        try:
            ok = await self.repo.delete(spec_id)
            if not ok:
                return self.create_error_result(ErrorCode.NOT_FOUND, f"规格不存在: {spec_id}")
            return self.create_success_result(True)
        except Exception as e:
            self.logger.error(f"删除规格失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.DELETE_FAILED, f"删除规格失败: {str(e)}")

    async def list_specs(self, product_id: int, params: PageParams) -> Result[SpecListResponse]:
        """获取商品所有规格（数据库级分页，支持with_total）"""
        try:
            items, total = await self.repo.get_paginated(
                page=params.page_num,
                page_size=params.page_size,
                with_total=True,
                product_id=product_id
            )
            total = total or 0
            page_count = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0
            resp = SpecListResponse(
                items=items,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )
            return self.create_success_result(resp)
        except Exception as e:
            self.logger.error(f"获取规格列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"获取规格列表失败: {str(e)}")

class SpecOptionService(BaseService[SpecOption, Result[SpecOptionResponse]]):
    """
    规格值服务类，提供规格值的增删改查
    """
    def __init__(self, repo: SpecOptionRepository):
        super().__init__()
        self.repo = repo

    async def create_option(self, option: SpecOptionCreate) -> Result:
        try:
            data = await self.repo.create(option)
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"创建规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"创建规格值失败: {str(e)}")

    async def get_option(self, option_id: int) -> Result:
        try:
            data = await self.repo.get(option_id)
            if not data:
                return ResultFactory.error(404, f"规格值不存在: {option_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"获取规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"获取规格值失败: {str(e)}")

    async def update_option(self, option_id: int, option: SpecOptionCreate) -> Result:
        try:
            data = await self.repo.update(option_id, option)
            if not data:
                return ResultFactory.error(404, f"规格值不存在: {option_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"更新规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"更新规格值失败: {str(e)}")

    async def delete_option(self, option_id: int) -> Result:
        try:
            ok = await self.repo.delete(option_id)
            if not ok:
                return ResultFactory.error(404, f"规格值不存在: {option_id}")
            return ResultFactory.success(True)
        except Exception as e:
            self.logger.error(f"删除规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"删除规格值失败: {str(e)}")

    async def list_options(self, spec_id: int, params: PageParams) -> Result[SpecOptionListResponse]:
        """获取指定规格的所有规格值（数据库级分页，支持with_total）"""
        try:
            items, total = await self.repo.get_paginated(
                page=params.page_num,
                page_size=params.page_size,
                with_total=True,
                spec_id=spec_id
            )
            total = total or 0
            page_count = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0
            resp = SpecOptionListResponse(
                items=items,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )
            return self.create_success_result(resp)
        except Exception as e:
            self.logger.error(f"获取规格值列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"获取规格值列表失败: {str(e)}")

class ProductSpecService(BaseService[ProductSpec, Result[ProductSpecResponse]]):
    """
    商品与规格关联服务类
    """
    def __init__(self, repo: ProductSpecRepository):
        super().__init__()
        self.repo = repo

    async def create_product_spec(self, product_spec: ProductSpecCreate) -> Result:
        try:
            data = await self.repo.create(product_spec)
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"创建商品规格失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"创建商品规格失败: {str(e)}")

    async def get_product_spec(self, product_spec_id: int) -> Result:
        try:
            data = await self.repo.get(product_spec_id)
            if not data:
                return ResultFactory.error(404, f"商品规格不存在: {product_spec_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"获取商品规格失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"获取商品规格失败: {str(e)}")

    async def delete_product_spec(self, product_spec_id: int) -> Result:
        try:
            ok = await self.repo.delete(product_spec_id)
            if not ok:
                return ResultFactory.error(404, f"商品规格不存在: {product_spec_id}")
            return ResultFactory.success(True)
        except Exception as e:
            self.logger.error(f"删除商品规格失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"删除商品规格失败: {str(e)}")

    async def list_product_specs(self, product_id: int) -> Result:
        try:
            data = await self.repo.list_by_product(product_id)
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"获取商品规格列表失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"获取商品规格列表失败: {str(e)}")

class ProductSpecOptionService(BaseService[ProductSpecOption, Result[ProductSpecOptionResponse]]):
    """
    商品与规格值关联服务类
    """
    def __init__(self, repo: ProductSpecOptionRepository):
        super().__init__()
        self.repo = repo

    async def create_product_spec_option(self, product_spec_option: ProductSpecOptionCreate) -> Result:
        try:
            data = await self.repo.create(product_spec_option)
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"创建商品规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"创建商品规格值失败: {str(e)}")

    async def get_product_spec_option(self, product_spec_option_id: int) -> Result:
        try:
            data = await self.repo.get(product_spec_option_id)
            if not data:
                return ResultFactory.error(404, f"商品规格值不存在: {product_spec_option_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"获取商品规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"获取商品规格值失败: {str(e)}")

    async def delete_product_spec_option(self, product_spec_option_id: int) -> Result:
        try:
            ok = await self.repo.delete(product_spec_option_id)
            if not ok:
                return ResultFactory.error(404, f"商品规格值不存在: {product_spec_option_id}")
            return ResultFactory.success(True)
        except Exception as e:
            self.logger.error(f"删除商品规格值失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"删除商品规格值失败: {str(e)}")

    async def list_product_spec_options(self, product_spec_id: int) -> Result:
        try:
            data = await self.repo.list_by_product_spec(product_spec_id)
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"获取商品规格值列表失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"获取商品规格值列表失败: {str(e)}")

class ProductSpecCombinationService(BaseService[ProductSpecCombination, Result[ProductSpecCombinationResponse]]):
    """
    商品规格组合（可售单元）服务类，包含组合生成
    Handles all business logic for product spec combinations.
    """
    def __init__(self, repo: ProductSpecCombinationRepository, album_service: AlbumService):
        super().__init__()
        self.repo = repo
        self.album_service = album_service

    async def create_combination(self, combination: ProductSpecCombinationCreate) -> Result:
        """创建商品规格组合，并为其关联一个新图册"""
        try:
            # 1. 创建图册
            album_create_data = AlbumCreate(name=f"SKU图册 - {combination.sku}", tags=["product", "sku"])
            album_result = await self.album_service.create_album(album_create_data)
            if not album_result.success:
                return ResultFactory.error(ErrorCode.CREATE_FAILED, f"创建SKU关联图册失败: {album_result.error_message}")
            
            created_album = album_result.data

            # 2. 创建规格组合并关联图册
            data = await self.repo.create_with_album(combination, created_album.id)
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"创建商品规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"创建商品规格组合失败: {str(e)}")

    async def get_combination(self, combination_id: int) -> Result:
        """
        获取单个规格组合
        """
        try:
            data = await self.repo.get(combination_id)
            if not data:
                return ResultFactory.error(404, f"商品规格组合不存在: {combination_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"获取商品规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"获取商品规格组合失败: {str(e)}")

    async def update_combination(self, combination_id: int, update_data: dict) -> Result:
        """
        更新规格组合
        """
        try:
            data = await self.repo.update(combination_id, update_data)
            if not data:
                return ResultFactory.error(404, f"规格组合不存在: {combination_id}")
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"更新规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"更新规格组合失败: {str(e)}")

    async def delete_combination(self, combination_id: int) -> Result:
        """删除商品规格组合，并同步删除关联的图册"""
        try:
            combination_to_delete = await self.repo.get_by_id(combination_id)
            if not combination_to_delete:
                return ResultFactory.error(ErrorCode.NOT_FOUND, f"商品规格组合不存在: {combination_id}")

            # 1. 逻辑删除关联的图册
            if combination_to_delete.album_id:
                await self.album_service.delete_album(combination_to_delete.album_id)

            # 2. 删除规格组合
            ok = await self.repo.delete(combination_to_delete)
            if not ok:
                 return ResultFactory.error(ErrorCode.DELETE_FAILED, "删除商品规格组合记录失败")

            return ResultFactory.success(True)
        except Exception as e:
            self.logger.error(f"删除商品规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(ErrorCode.DELETE_FAILED, f"删除商品规格组合失败: {str(e)}")

    async def list_combinations(self, product_id: int, params: PageParams) -> Result[ProductSpecCombinationListResponse]:
        """获取指定商品的所有规格组合（数据库级分页，支持with_total）"""
        try:
            items, total = await self.repo.get_paginated(
                page=params.page_num,
                page_size=params.page_size,
                with_total=True,
                product_id=product_id
            )
            total = total or 0
            page_count = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0
            resp = ProductSpecCombinationListResponse(
                items=items,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )
            return self.create_success_result(resp)
        except Exception as e:
            self.logger.error(f"获取规格组合列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"获取规格组合列表失败: {str(e)}")

    def generate_combinations(self, spec_option_id_lists: List[List[int]]) -> Result[List[List[int]]]:
        """
        生成所有规格值ID的组合（笛卡尔积）
        """
        try:
            from itertools import product
            data = [list(comb) for comb in product(*spec_option_id_lists)]
            return ResultFactory.success(data)
        except Exception as e:
            self.logger.error(f"生成规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"生成规格组合失败: {str(e)}")

    def _generate_sku(self, product_id: int, option_ids: List[int]) -> str:
        """
        生成唯一SKU
        """
        return f"{product_id}-{'-'.join(map(str, option_ids))}"

    async def generate_and_save_combinations(
        self,
        product_id: int,
        spec_service,
        spec_option_service
    ) -> Result:
        """
        生成并批量保存所有商品规格组合，返回新建的组合列表
        """
        try:
            # 1. 获取商品所有规格
            spec_result = await spec_service.list_specs(product_id)
            if not spec_result.success or not spec_result.data:
                return ResultFactory.error(404, f"未找到商品规格: {product_id}")
            specs = spec_result.data
            if not specs:
                return ResultFactory.error(400, "商品未配置任何规格")

            # 2. 获取每个规格的规格值ID列表
            spec_option_id_lists: List[List[int]] = []
            for spec in specs:
                option_result = await spec_option_service.list_options(spec.id)
                if not option_result.success or not option_result.data:
                    return ResultFactory.error(400, f"规格[{spec.name}]未配置规格值")
                option_ids = [opt.id for opt in option_result.data]
                if not option_ids:
                    return ResultFactory.error(400, f"规格[{spec.name}]未配置规格值")
                spec_option_id_lists.append(option_ids)

            # 3. 生成所有组合
            comb_result = self.generate_combinations(spec_option_id_lists)
            if not comb_result.success or not comb_result.data:
                return ResultFactory.error(400, "生成规格组合失败")
            combinations = comb_result.data
            if not combinations:
                return ResultFactory.error(400, "无可用规格组合")

            # 4. 检查SKU唯一性，组装批量插入对象
            exist_result = await self.repo.list_by_product(product_id)
            exist_skus = set([c.sku for c in exist_result]) if exist_result else set()
            new_combinations: List[ProductSpecCombinationCreate] = []
            for option_ids in combinations:
                sku = self._generate_sku(product_id, option_ids)
                if sku in exist_skus:
                    continue  # 跳过已存在SKU
                new_combinations.append(ProductSpecCombinationCreate(
                    product_id=product_id,
                    sku=sku,
                    price=None,
                    stock_quantity=0,
                    spec_option_ids=option_ids
                ))
            if not new_combinations:
                return ResultFactory.error(400, "所有组合已存在，无需生成")

            # 5. 批量保存
            objs = await self.repo.bulk_create(new_combinations)
            return ResultFactory.success(objs)
        except Exception as e:
            self.logger.error(f"生成并保存规格组合失败: {str(e)}", exc_info=True)
            return ResultFactory.error(400, f"生成并保存规格组合失败: {str(e)}") 