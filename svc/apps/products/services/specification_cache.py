"""
产品规格缓存管理
提供多层缓存策略，提升查询性能
"""
import json
import hashlib
from typing import Optional, Dict, List, Any
from redis.asyncio import Redis
import logging

from svc.apps.products.models.specs import ProductSpecCombination
from svc.apps.products.schemas.specs import ProductSpecificationResponse

logger = logging.getLogger(__name__)


class ProductSpecificationCache:
    """产品规格缓存管理器"""
    
    def __init__(self, redis: Optional[Redis] = None):
        self.redis = redis
        self.default_ttl = 3600  # 1小时
        self.stats_ttl = 1800    # 30分钟
        self.search_ttl = 600    # 10分钟
        
        # 缓存键前缀
        self.prefix = "product_spec"
        self.specs_key = f"{self.prefix}:specs"
        self.combinations_key = f"{self.prefix}:combinations"
        self.stats_key = f"{self.prefix}:stats"
        self.search_key = f"{self.prefix}:search"
    
    def _make_key(self, key_type: str, *args) -> str:
        """生成缓存键"""
        parts = [key_type] + [str(arg) for arg in args]
        return ":".join(parts)
    
    def _hash_params(self, params: Dict) -> str:
        """对参数进行哈希，用于缓存键"""
        param_str = json.dumps(params, sort_keys=True, default=str)
        return hashlib.md5(param_str.encode()).hexdigest()[:8]
    
    async def get_product_specifications(self, product_id: int) -> Optional[Dict]:
        """获取产品完整规格信息缓存"""
        if not self.redis:
            return None
        
        try:
            key = self._make_key(self.specs_key, product_id)
            data = await self.redis.get(key)
            if data:
                logger.debug(f"缓存命中: 产品规格 {product_id}")
                return json.loads(data)
        except Exception as e:
            logger.warning(f"获取规格缓存失败: {e}")
        
        return None
    
    async def set_product_specifications(
        self, 
        product_id: int, 
        data: ProductSpecificationResponse,
        ttl: Optional[int] = None
    ):
        """设置产品完整规格信息缓存"""
        if not self.redis:
            return
        
        try:
            key = self._make_key(self.specs_key, product_id)
            ttl = ttl or self.default_ttl
            
            # 序列化数据
            if hasattr(data, 'model_dump'):
                cache_data = data.model_dump()
            else:
                cache_data = data
            
            await self.redis.setex(
                key, 
                ttl, 
                json.dumps(cache_data, default=str, ensure_ascii=False)
            )
            logger.debug(f"缓存设置: 产品规格 {product_id}, TTL={ttl}")
            
        except Exception as e:
            logger.warning(f"设置规格缓存失败: {e}")
    
    async def get_combinations_by_product(
        self, 
        product_id: int, 
        filters: Optional[Dict] = None
    ) -> Optional[List[Dict]]:
        """获取产品组合缓存"""
        if not self.redis:
            return None
        
        try:
            # 生成包含过滤条件的缓存键
            filter_hash = self._hash_params(filters or {})
            key = self._make_key(self.combinations_key, product_id, filter_hash)
            
            data = await self.redis.get(key)
            if data:
                logger.debug(f"缓存命中: 产品组合 {product_id}")
                return json.loads(data)
        except Exception as e:
            logger.warning(f"获取组合缓存失败: {e}")
        
        return None
    
    async def set_combinations_by_product(
        self, 
        product_id: int, 
        combinations: List[ProductSpecCombination],
        filters: Optional[Dict] = None,
        ttl: Optional[int] = None
    ):
        """设置产品组合缓存"""
        if not self.redis:
            return
        
        try:
            filter_hash = self._hash_params(filters or {})
            key = self._make_key(self.combinations_key, product_id, filter_hash)
            ttl = ttl or self.default_ttl
            
            # 序列化组合数据
            cache_data = []
            for combo in combinations:
                if hasattr(combo, 'to_dict'):
                    cache_data.append(combo.to_dict())
                else:
                    cache_data.append({
                        'id': combo.id,
                        'product_id': combo.product_id,
                        'sku': combo.sku,
                        'price': combo.price,
                        'stock_quantity': combo.stock_quantity,
                        # 添加其他需要缓存的字段
                    })
            
            await self.redis.setex(
                key, 
                ttl, 
                json.dumps(cache_data, default=str, ensure_ascii=False)
            )
            logger.debug(f"缓存设置: 产品组合 {product_id}, 数量={len(combinations)}")
            
        except Exception as e:
            logger.warning(f"设置组合缓存失败: {e}")
    
    async def get_specification_stats(self, product_id: int) -> Optional[Dict]:
        """获取规格统计信息缓存"""
        if not self.redis:
            return None
        
        try:
            key = self._make_key(self.stats_key, product_id)
            data = await self.redis.get(key)
            if data:
                logger.debug(f"缓存命中: 规格统计 {product_id}")
                return json.loads(data)
        except Exception as e:
            logger.warning(f"获取统计缓存失败: {e}")
        
        return None
    
    async def set_specification_stats(
        self, 
        product_id: int, 
        stats: Dict,
        ttl: Optional[int] = None
    ):
        """设置规格统计信息缓存"""
        if not self.redis:
            return
        
        try:
            key = self._make_key(self.stats_key, product_id)
            ttl = ttl or self.stats_ttl
            
            await self.redis.setex(
                key, 
                ttl, 
                json.dumps(stats, default=str, ensure_ascii=False)
            )
            logger.debug(f"缓存设置: 规格统计 {product_id}")
            
        except Exception as e:
            logger.warning(f"设置统计缓存失败: {e}")
    
    async def get_search_results(
        self, 
        product_id: int, 
        search_params: Dict
    ) -> Optional[List[Dict]]:
        """获取搜索结果缓存"""
        if not self.redis:
            return None
        
        try:
            param_hash = self._hash_params(search_params)
            key = self._make_key(self.search_key, product_id, param_hash)
            
            data = await self.redis.get(key)
            if data:
                logger.debug(f"缓存命中: 搜索结果 {product_id}")
                return json.loads(data)
        except Exception as e:
            logger.warning(f"获取搜索缓存失败: {e}")
        
        return None
    
    async def set_search_results(
        self, 
        product_id: int, 
        search_params: Dict,
        results: List[Dict],
        ttl: Optional[int] = None
    ):
        """设置搜索结果缓存"""
        if not self.redis:
            return
        
        try:
            param_hash = self._hash_params(search_params)
            key = self._make_key(self.search_key, product_id, param_hash)
            ttl = ttl or self.search_ttl
            
            await self.redis.setex(
                key, 
                ttl, 
                json.dumps(results, default=str, ensure_ascii=False)
            )
            logger.debug(f"缓存设置: 搜索结果 {product_id}, 数量={len(results)}")
            
        except Exception as e:
            logger.warning(f"设置搜索缓存失败: {e}")
    
    async def invalidate_product_cache(self, product_id: int):
        """失效产品相关的所有缓存"""
        if not self.redis:
            return
        
        try:
            # 构建模式匹配的键
            patterns = [
                f"{self.specs_key}:{product_id}",
                f"{self.combinations_key}:{product_id}:*",
                f"{self.stats_key}:{product_id}",
                f"{self.search_key}:{product_id}:*"
            ]
            
            deleted_count = 0
            for pattern in patterns:
                if "*" in pattern:
                    # 使用SCAN命令查找匹配的键
                    keys = []
                    async for key in self.redis.scan_iter(match=pattern):
                        keys.append(key)
                    
                    if keys:
                        deleted_count += await self.redis.delete(*keys)
                else:
                    # 直接删除单个键
                    deleted_count += await self.redis.delete(pattern)
            
            logger.info(f"缓存失效: 产品 {product_id}, 删除键数量={deleted_count}")
            
        except Exception as e:
            logger.warning(f"失效缓存失败: {e}")
    
    async def invalidate_all_cache(self):
        """失效所有产品规格缓存"""
        if not self.redis:
            return
        
        try:
            pattern = f"{self.prefix}:*"
            keys = []
            async for key in self.redis.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                deleted_count = await self.redis.delete(*keys)
                logger.info(f"全量缓存失效: 删除键数量={deleted_count}")
            
        except Exception as e:
            logger.warning(f"全量缓存失效失败: {e}")
    
    async def get_cache_info(self, product_id: int) -> Dict:
        """获取缓存信息"""
        if not self.redis:
            return {"cache_enabled": False}
        
        try:
            info = {"cache_enabled": True, "keys": {}}
            
            # 检查各类缓存是否存在
            keys_to_check = {
                "specifications": self._make_key(self.specs_key, product_id),
                "stats": self._make_key(self.stats_key, product_id)
            }
            
            for cache_type, key in keys_to_check.items():
                exists = await self.redis.exists(key)
                if exists:
                    ttl = await self.redis.ttl(key)
                    info["keys"][cache_type] = {"exists": True, "ttl": ttl}
                else:
                    info["keys"][cache_type] = {"exists": False, "ttl": -1}
            
            return info
            
        except Exception as e:
            logger.warning(f"获取缓存信息失败: {e}")
            return {"cache_enabled": True, "error": str(e)}
