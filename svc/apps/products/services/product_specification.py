"""
产品规格统一服务
整合所有规格相关的业务逻辑，提供一站式服务
"""
from itertools import product
from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis

from svc.apps.albums.services.album import AlbumService
from svc.apps.products.models.product import Product
from svc.apps.products.models.specs import (ProductSpecCombination, Spec,
                                            SpecOption)
from svc.apps.products.repositories.product_specification import \
    ProductSpecificationRepository
from svc.apps.products.repositories.specs import (SpecOptionRepository,
                                                  SpecRepository)
from svc.apps.products.schemas.specs import (ProductSpecCombinationCreate,
                                             ProductSpecCombinationResponse,
                                             ProductSpecificationResponse,
                                             SpecCreate, SpecOptionCreate,
                                             SpecWithOptions)
from svc.core.database.transactions import with_transaction
from svc.core.events.event_names import (SYSTEM_AUDIT_LOG_RECORDED,
                                         SYSTEM_CACHE_INVALIDATION_REQUESTED)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.base import BatchUpdateMixin
from svc.core.services.result import Result


class ProductSpecificationService(BaseService[ProductSpecCombination, Result[ProductSpecCombinationResponse]], BatchUpdateMixin):
    """产品规格统一服务"""

    resource_type = "product_specification"
    
    def __init__(
        self,
        redis: Optional[Redis] = None,
        spec_repo: Optional[ProductSpecificationRepository] = None,
        basic_spec_repo: Optional[SpecRepository] = None,
        spec_option_repo: Optional[SpecOptionRepository] = None,
        album_service: Optional[AlbumService] = None
    ):
        super().__init__(redis)
        self.spec_repo = spec_repo
        self.basic_spec_repo = basic_spec_repo
        self.spec_option_repo = spec_option_repo
        self.album_service = album_service
    
    @with_transaction(auto_commit=True)
    async def setup_product_specifications(
        self,
        product_id: int,
        specs_data: List[SpecWithOptions],
        user_id: Optional[int] = None,
        db=None
    ) -> Result[List[ProductSpecCombinationResponse]]:
        """一站式设置产品规格"""
        # 事务管理由装饰器处理，这里直接执行业务逻辑
        # 1. 验证产品存在
        if not await self._validate_product_exists(product_id):
            return self.create_error_result(ErrorCode.NOT_FOUND, "产品不存在")

        # 2. 清理现有规格组合
        await self.spec_repo.delete_product_combinations(product_id)

        # 3. 创建或获取规格和规格值
        spec_options_map = await self._ensure_specs_and_options(specs_data)

        if not spec_options_map:
            return self.create_error_result(ErrorCode.VALIDATION_ERROR, "未找到有效的规格配置")

        # 4. 生成所有可能的组合
        combinations_data = await self._generate_all_combinations(
            product_id, spec_options_map
        )

        if not combinations_data:
            return self.create_error_result(ErrorCode.VALIDATION_ERROR, "无法生成有效的规格组合")

        # 5. 批量创建组合
        created_combinations = await self.spec_repo.bulk_create_combinations(combinations_data)

        # 6. 触发事件
        await self._dispatch_specification_events(
            product_id, created_combinations, user_id, "created"
        )

        # 7. 构建响应
        responses = [
            await self._build_combination_response(combo)
            for combo in created_combinations
        ]

        self.logger.info(f"成功设置产品规格: product_id={product_id}, 组合数量={len(responses)}")
        return self.create_success_result(responses)
    
    async def get_product_specifications(
        self,
        product_id: int,
        include_inactive: bool = False
    ) -> Result[ProductSpecificationResponse]:
        """获取产品完整规格信息"""
        try:
            # 1. 尝试从缓存获取
            if not include_inactive:  # 只缓存活跃数据
                cache_key = f"product_specs:{product_id}"
                cached_data = await self.get_cached_resource(cache_key)
                if cached_data:
                    return self.create_success_result(cached_data)

            # 2. 从数据库获取规格和选项
            specs_with_options = await self.spec_repo.get_product_specs_with_options(product_id)

            # 3. 获取组合信息
            combinations = await self.spec_repo.get_combinations_with_specs(
                product_id, include_inactive=include_inactive
            )

            # 4. 获取统计信息
            stats = await self.spec_repo.get_combination_stats(product_id)

            # 5. 构建响应数据
            response_data = ProductSpecificationResponse(
                product_id=product_id,
                specs=specs_with_options,
                combinations=[
                    await self._build_combination_response(combo) for combo in combinations
                ],
                stats=stats
            )

            # 6. 缓存结果（只缓存活跃数据）
            if not include_inactive:
                cache_key = f"product_specs:{product_id}"
                await self.cache_resource(cache_key, response_data)

            return self.create_success_result(response_data)

        except Exception as e:
            self.logger.error(f"获取产品规格失败: product_id={product_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"获取规格失败: {str(e)}")
    
    async def update_combination_pricing(
        self,
        combination_ids: List[int],
        pricing_data: Dict[str, Any],
        user_id: Optional[int] = None
    ) -> Result[List[ProductSpecCombinationResponse]]:
        """批量更新组合价格"""
        # 使用通用批量更新方法
        return await self.batch_update_resources(
            resource_ids=combination_ids,
            update_data=pricing_data,
            repository=self.spec_repo,
            resource_type="规格组合",
            event_prefix="product:combination",
            cache_key_generator=lambda combo_id: f"product_specs:combination:{combo_id}"
        )
    
    @with_transaction(auto_commit=True)
    async def copy_specifications(
        self,
        source_product_id: int,
        target_product_id: int,
        copy_pricing: bool = False,
        copy_stock: bool = False,
        user_id: Optional[int] = None,
        db=None
    ) -> Result[List[ProductSpecCombinationResponse]]:
        """复制规格配置到另一个产品"""
        # 1. 获取源产品规格
        source_result = await self.get_product_specifications(source_product_id)
        if not source_result.is_success:
            return source_result

        source_data = source_result.data

        # 2. 构建目标产品规格数据
        target_specs_data = self._transform_specs_for_copy(
            source_data.specs, copy_pricing, copy_stock
        )

        # 3. 设置目标产品规格
        result = await self.setup_product_specifications(
            target_product_id, target_specs_data, user_id
        )

        if result.is_success:
            self.logger.info(f"复制规格成功: source={source_product_id}, target={target_product_id}")

        return result
    
    # 私有辅助方法
    async def _validate_product_exists(self, product_id: int) -> bool:
        """验证产品是否存在"""
        from sqlalchemy import select
        query = select(Product).where(Product.id == product_id)
        result = await self.spec_repo.db.execute(query)
        return result.scalars().first() is not None
    
    async def _ensure_specs_and_options(self, specs_data: List[SpecWithOptions]) -> Dict[int, List[int]]:
        """确保规格和规格值存在，返回规格ID到选项ID列表的映射"""
        spec_options_map = {}
        
        for spec_data in specs_data:
            # 创建或获取规格
            spec = await self._get_or_create_spec(spec_data)
            if not spec:
                continue
            
            # 创建或获取规格值
            option_ids = []
            for option_value in spec_data.options:
                option = await self._get_or_create_spec_option(spec.id, option_value)
                if option:
                    option_ids.append(option.id)
            
            if option_ids:
                spec_options_map[spec.id] = option_ids
        
        return spec_options_map
    
    async def _get_or_create_spec(self, spec_data: SpecWithOptions) -> Optional[Spec]:
        """获取或创建规格"""
        # 先尝试根据名称查找
        existing_spec = await self.basic_spec_repo.get_one(name=spec_data.name)
        if existing_spec:
            return existing_spec
        
        # 创建新规格
        spec_create = SpecCreate(
            name=spec_data.name,
            description=spec_data.description,
            sort_order=spec_data.sort_order
        )
        return await self.basic_spec_repo.create(spec_create)
    
    async def _get_or_create_spec_option(self, spec_id: int, value: str) -> Optional[SpecOption]:
        """获取或创建规格值"""
        # 先尝试查找
        existing_option = await self.spec_option_repo.get_one(spec_id=spec_id, value=value)
        if existing_option:
            return existing_option
        
        # 创建新规格值
        option_create = SpecOptionCreate(spec_id=spec_id, value=value)
        return await self.spec_option_repo.create(option_create)
    
    async def _generate_all_combinations(self, product_id: int, spec_options_map: Dict[int, List[int]]) -> List[Dict]:
        """生成所有规格组合"""
        # 获取所有规格的选项ID列表
        option_lists = list(spec_options_map.values())
        if not option_lists:
            return []
        
        combinations = []
        for option_combination in product(*option_lists):
            sku = await self.spec_repo.generate_sku(product_id, list(option_combination))
            combinations.append({
                'product_id': product_id,
                'sku': sku,
                'spec_option_ids': list(option_combination),
                'stock_quantity': 0,
                'is_active': True
            })
        
        return combinations

    async def _build_combination_response(self, combo: ProductSpecCombination) -> ProductSpecCombinationResponse:
        """构建组合响应对象"""
        combo_dict = {
            'id': combo.id,
            'product_id': combo.product_id,
            'sku': combo.sku,
            'price': combo.price / 100 if combo.price else None,  # 转换为元
            'cost_price': combo.cost_price / 100 if combo.cost_price else None,
            'market_price': combo.market_price / 100 if combo.market_price else None,
            'stock_quantity': combo.stock_quantity,
            'min_stock_level': combo.min_stock_level,
            'max_stock_level': combo.max_stock_level,
            'is_active': combo.is_active,
            'is_default': combo.is_default,
            'weight': combo.weight,
            'barcode': combo.barcode,
            'description': combo.description,
            'spec_option_ids': [opt.id for opt in combo.spec_options] if combo.spec_options else [],
            'album_id': combo.album_id,
            'album': combo.album.to_dict() if combo.album else None
        }
        return ProductSpecCombinationResponse(**combo_dict)

    def _transform_specs_for_copy(
        self,
        source_specs: List[Dict],
        copy_pricing: bool = False,
        copy_stock: bool = False
    ) -> List[SpecWithOptions]:
        """转换规格数据用于复制"""
        transformed_specs = []

        for spec in source_specs:
            spec_with_options = SpecWithOptions(
                name=spec['name'],
                description=spec.get('description'),
                sort_order=spec.get('sort_order', 0),
                options=[opt['value'] for opt in spec.get('options', [])]
            )
            transformed_specs.append(spec_with_options)

        return transformed_specs

    async def _dispatch_specification_events(
        self,
        product_id: int,
        combinations: List[ProductSpecCombination],
        user_id: Optional[int] = None,
        action: str = "updated"
    ):
        """触发规格相关事件"""
        try:
            # 触发缓存失效事件
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={
                "resource_type": "product",
                "resource_id": product_id
            })

            # 直接失效本地缓存
            cache_key = f"product_specs:{product_id}"
            await self.invalidate_cache(cache_key)

            # 触发审计日志事件
            if user_id:
                dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                    "user_id": user_id,
                    "action": f"product_specifications_{action}",
                    "resource_type": self.resource_type,
                    "resource_id": product_id,
                    "metadata": {
                        "combination_count": len(combinations),
                        "combination_ids": [c.id for c in combinations]
                    }
                })

        except Exception as e:
            self.logger.warning(f"触发规格事件失败: {str(e)}")

    async def _dispatch_pricing_events(
        self,
        combinations: List[ProductSpecCombination],
        user_id: Optional[int] = None
    ):
        """触发价格更新事件"""
        try:
            # 获取产品ID（假设所有组合属于同一产品）
            product_id = combinations[0].product_id if combinations else None

            if product_id:
                # 触发缓存失效事件
                dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={
                    "resource_type": "product",
                    "resource_id": product_id
                })

                # 触发审计日志事件
                if user_id:
                    dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                        "user_id": user_id,
                        "action": "combination_pricing_updated",
                        "resource_type": self.resource_type,
                        "resource_id": product_id,
                        "metadata": {
                            "updated_combinations": len(combinations),
                            "combination_ids": [c.id for c in combinations]
                        }
                    })

        except Exception as e:
            self.logger.warning(f"触发价格事件失败: {str(e)}")

    async def get_resource_by_id(self, resource_id: int) -> Optional[ProductSpecCombination]:
        """获取指定ID的规格组合资源"""
        return await self.spec_repo.get_by_id(resource_id)

    # 便捷查询方法
    async def find_combinations_by_specs(
        self,
        product_id: int,
        spec_option_ids: List[int]
    ) -> Result[List[ProductSpecCombinationResponse]]:
        """根据规格值查找组合"""
        try:
            combinations = await self.spec_repo.find_combinations_by_specs(product_id, spec_option_ids)
            responses = [
                await self._build_combination_response(combo) for combo in combinations
            ]
            return self.create_success_result(responses)
        except Exception as e:
            self.logger.error(f"根据规格查找组合失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"查找组合失败: {str(e)}")

    async def get_combinations_by_price_range(
        self,
        product_id: int,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None
    ) -> Result[List[ProductSpecCombinationResponse]]:
        """根据价格范围查找组合"""
        try:
            combinations = await self.spec_repo.get_combinations_by_price_range(
                product_id, min_price, max_price
            )
            responses = [
                await self._build_combination_response(combo) for combo in combinations
            ]
            return self.create_success_result(responses)
        except Exception as e:
            self.logger.error(f"根据价格查找组合失败: {str(e)}", exc_info=True)
            return self.create_error_result(ErrorCode.OPERATION_FAILED, f"查找组合失败: {str(e)}")
