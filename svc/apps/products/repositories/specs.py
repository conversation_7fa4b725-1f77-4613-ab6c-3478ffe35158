from typing import List, Optional, Union

from sqlalchemy import Column, and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload

from svc.apps.products.models.specs import (ProductSpecCombination, Spec,
                                            SpecOption,
                                            combination_spec_options)
from svc.apps.products.schemas.specs import (ProductSpecCombinationCreate,
                                             SpecCreate, SpecOptionCreate)
from svc.core.repositories import BaseRepository


class SpecRepository(BaseRepository[Spec, SpecCreate, SpecCreate]):
    """
    规格仓库类，提供规格的基本CRUD操作
    优化查询性能，解决N+1问题
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, Spec)

    async def get_specs_with_options(self, spec_ids: Optional[List[int]] = None) -> List[Spec]:
        """获取规格及其选项，使用预加载避免N+1问题"""
        query = select(Spec).options(
            selectinload(Spec.options)
        ).where(Spec.is_active == True)

        if spec_ids:
            query = query.where(Spec.id.in_(spec_ids))

        query = query.order_by(Spec.sort_order, Spec.name)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_active_specs(self) -> List[Spec]:
        """获取所有启用的规格"""
        return await self.get_specs_with_options()

    async def search_specs(self, keyword: str) -> List[Spec]:
        """搜索规格"""
        query = select(Spec).options(
            selectinload(Spec.options)
        ).where(
            and_(
                Spec.is_active == True,
                Spec.name.ilike(f"%{keyword}%")
            )
        ).order_by(Spec.sort_order, Spec.name)

        result = await self.db.execute(query)
        return result.scalars().all()

class SpecOptionRepository(BaseRepository[SpecOption, SpecOptionCreate, SpecOptionCreate]):
    """
    规格值仓库类，提供规格值的基本CRUD操作
    优化查询性能，支持批量操作
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, SpecOption)

    async def get_options_by_spec(self, spec_id: int, include_inactive: bool = False) -> List[SpecOption]:
        """获取指定规格的所有规格值"""
        query = select(SpecOption).options(
            joinedload(SpecOption.spec)
        ).where(SpecOption.spec_id == spec_id)

        if not include_inactive:
            query = query.where(SpecOption.is_active == True)

        query = query.order_by(SpecOption.sort_order, SpecOption.value)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_options_by_ids(self, option_ids: List[int]) -> List[SpecOption]:
        """批量获取规格值"""
        query = select(SpecOption).options(
            joinedload(SpecOption.spec)
        ).where(
            and_(
                SpecOption.id.in_(option_ids),
                SpecOption.is_active == True
            )
        ).order_by(SpecOption.spec_id, SpecOption.sort_order)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def search_options(self, keyword: str, spec_id: Optional[int] = None) -> List[SpecOption]:
        """搜索规格值"""
        query = select(SpecOption).options(
            joinedload(SpecOption.spec)
        ).where(
            and_(
                SpecOption.is_active == True,
                SpecOption.value.ilike(f"%{keyword}%")
            )
        )

        if spec_id:
            query = query.where(SpecOption.spec_id == spec_id)

        query = query.order_by(SpecOption.spec_id, SpecOption.sort_order)

        result = await self.db.execute(query)
        return result.scalars().all()

# 删除冗余的ProductSpecRepository和ProductSpecOptionRepository
# 这些功能已经通过ProductSpecificationRepository实现

class ProductSpecCombinationRepository(BaseRepository[ProductSpecCombination, ProductSpecCombinationCreate, ProductSpecCombinationCreate]):
    """
    商品规格组合（可售单元）仓库类
    重构后的版本，支持新的关联表结构
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSpecCombination)

    async def create_with_album(self, combination_data: ProductSpecCombinationCreate, album_id: int) -> ProductSpecCombination:
        """创建规格组合并关联图册ID"""
        data_dict = combination_data.model_dump()
        spec_option_ids = data_dict.pop('spec_option_ids', [])

        new_combination = ProductSpecCombination(**data_dict, album_id=album_id)
        self.db.add(new_combination)
        await self.db.flush()

        # 添加规格值关联
        if spec_option_ids:
            for option_id in spec_option_ids:
                await self.db.execute(
                    combination_spec_options.insert().values(
                        combination_id=new_combination.id,
                        spec_option_id=option_id
                    )
                )

        await self.db.flush()
        await self.db.refresh(new_combination)
        return new_combination

    async def get_one(
        self,
        **filters
    ) -> Optional[ProductSpecCombination]:
        """根据条件获取单个实体，并预加载图册和规格值"""
        if not filters:
            return None

        query = select(self.model).options(
            selectinload(self.model.album),
            selectinload(self.model.spec_options).selectinload(SpecOption.spec)
        )
        query = self._apply_filters(query, **filters)

        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_list(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        **filters
    ) -> List[ProductSpecCombination]:
        """获取实体列表，并预加载图册和规格值"""
        query = self._build_query(order_by, order_direction, **filters).options(
            selectinload(self.model.album),
            selectinload(self.model.spec_options).selectinload(SpecOption.spec)
        )

        query = query.offset(skip).limit(limit)

        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def list_by_product(self, product_id: int, skip: int = 0, limit: int = 100) -> List[ProductSpecCombination]:
        """
        获取指定商品的所有规格组合，支持数据库级分页
        """
        return await self.get_list(product_id=product_id, skip=skip, limit=limit)

    async def bulk_create(self, combinations: List[ProductSpecCombinationCreate]) -> List[ProductSpecCombination]:
        """
        批量插入商品规格组合，支持新的关联表结构
        """
        objs = []
        for combo_data in combinations:
            data_dict = combo_data.model_dump()
            spec_option_ids = data_dict.pop('spec_option_ids', [])

            obj = ProductSpecCombination(**data_dict)
            self.db.add(obj)
            await self.db.flush()  # 获取ID

            # 添加规格值关联
            if spec_option_ids:
                for option_id in spec_option_ids:
                    await self.db.execute(
                        combination_spec_options.insert().values(
                            combination_id=obj.id,
                            spec_option_id=option_id
                        )
                    )

            objs.append(obj)

        await self.db.flush()
        return objs