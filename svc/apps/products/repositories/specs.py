from typing import List, Optional, Union

from sqlalchemy import Column, and_, asc, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload

from svc.apps.products.models.specs import (ProductSpecCombination, Spec,
                                            SpecOption,
                                            combination_spec_options)
from svc.apps.products.schemas.specs import (ProductSpecCombinationCreate,
                                             SpecCreate, SpecOptionCreate)
from svc.core.repositories import BaseRepository


class SpecRepository(BaseRepository[Spec, SpecCreate, SpecCreate]):
    """
    规格仓库类，提供规格的基本CRUD操作
    优化查询性能，解决N+1问题
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, Spec)

    async def get_specs_with_options(self, spec_ids: Optional[List[int]] = None) -> List[Spec]:
        """获取规格及其选项，使用预加载避免N+1问题"""
        query = select(Spec).options(
            selectinload(Spec.options)
        ).where(Spec.is_active == True)

        if spec_ids:
            query = query.where(Spec.id.in_(spec_ids))

        query = query.order_by(Spec.sort_order, Spec.name)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_active_specs(self) -> List[Spec]:
        """获取所有启用的规格"""
        return await self.get_specs_with_options()

    async def search_specs(self, keyword: str) -> List[Spec]:
        """搜索规格"""
        query = select(Spec).options(
            selectinload(Spec.options)
        ).where(
            and_(
                Spec.is_active == True,
                Spec.name.ilike(f"%{keyword}%")
            )
        ).order_by(Spec.sort_order, Spec.name)

        result = await self.db.execute(query)
        return result.scalars().all()

class SpecOptionRepository(BaseRepository[SpecOption, SpecOptionCreate, SpecOptionCreate]):
    """
    规格值仓库类，提供规格值的基本CRUD操作
    优化查询性能，支持批量操作
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, SpecOption)

    async def get_options_by_spec(self, spec_id: int, include_inactive: bool = False) -> List[SpecOption]:
        """获取指定规格的所有规格值"""
        query = select(SpecOption).options(
            joinedload(SpecOption.spec)
        ).where(SpecOption.spec_id == spec_id)

        if not include_inactive:
            query = query.where(SpecOption.is_active == True)

        query = query.order_by(SpecOption.sort_order, SpecOption.value)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_options_by_ids(self, option_ids: List[int]) -> List[SpecOption]:
        """批量获取规格值"""
        query = select(SpecOption).options(
            joinedload(SpecOption.spec)
        ).where(
            and_(
                SpecOption.id.in_(option_ids),
                SpecOption.is_active == True
            )
        ).order_by(SpecOption.spec_id, SpecOption.sort_order)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def search_options(self, keyword: str, spec_id: Optional[int] = None) -> List[SpecOption]:
        """搜索规格值"""
        query = select(SpecOption).options(
            joinedload(SpecOption.spec)
        ).where(
            and_(
                SpecOption.is_active == True,
                SpecOption.value.ilike(f"%{keyword}%")
            )
        )

        if spec_id:
            query = query.where(SpecOption.spec_id == spec_id)

        query = query.order_by(SpecOption.spec_id, SpecOption.sort_order)

        result = await self.db.execute(query)
        return result.scalars().all()

# 删除冗余的ProductSpecRepository和ProductSpecOptionRepository
# 这些功能已经通过ProductSpecificationRepository实现

class ProductSpecCombinationRepository(BaseRepository[ProductSpecCombination, ProductSpecCombinationCreate, ProductSpecCombinationCreate]):
    """
    商品规格组合（可售单元）仓库类
    重构后的版本，支持新的关联表结构
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSpecCombination)

    async def create_with_album(self, combination_data: ProductSpecCombinationCreate, album_id: int) -> ProductSpecCombination:
        """创建规格组合并关联图册ID"""
        data_dict = combination_data.model_dump()
        spec_option_ids = data_dict.pop('spec_option_ids', [])

        new_combination = ProductSpecCombination(**data_dict, album_id=album_id)
        self.db.add(new_combination)
        await self.db.flush()

        # 添加规格值关联
        if spec_option_ids:
            for option_id in spec_option_ids:
                await self.db.execute(
                    combination_spec_options.insert().values(
                        combination_id=new_combination.id,
                        spec_option_id=option_id
                    )
                )

        await self.db.flush()
        await self.db.refresh(new_combination)
        return new_combination

    async def get_one(
        self,
        **filters
    ) -> Optional[ProductSpecCombination]:
        """根据条件获取单个实体，并预加载图册和规格值"""
        if not filters:
            return None

        query = select(self.model).options(
            selectinload(self.model.album),
            selectinload(self.model.spec_options).selectinload(SpecOption.spec)
        )
        query = self._apply_filters(query, **filters)

        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_list(
        self,
        *,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        **filters
    ) -> List[ProductSpecCombination]:
        """获取实体列表，并预加载图册和规格值"""
        query = self._build_query(order_by, order_direction, **filters).options(
            selectinload(self.model.album),
            selectinload(self.model.spec_options).selectinload(SpecOption.spec)
        )

        query = query.offset(skip).limit(limit)

        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def list_by_product(self, product_id: int, skip: int = 0, limit: int = 100) -> List[ProductSpecCombination]:
        """
        获取指定商品的所有规格组合，支持数据库级分页
        """
        return await self.get_list(product_id=product_id, skip=skip, limit=limit)

    async def bulk_create(self, combinations: List[ProductSpecCombinationCreate]) -> List[ProductSpecCombination]:
        """
        批量插入商品规格组合，支持新的关联表结构
        """
        objs = []
        for combo_data in combinations:
            data_dict = combo_data.model_dump()
            spec_option_ids = data_dict.pop('spec_option_ids', [])

            obj = ProductSpecCombination(**data_dict)
            self.db.add(obj)
            await self.db.flush()  # 获取ID

            # 添加规格值关联
            if spec_option_ids:
                for option_id in spec_option_ids:
                    await self.db.execute(
                        combination_spec_options.insert().values(
                            combination_id=obj.id,
                            spec_option_id=option_id
                        )
                    )

            objs.append(obj)

        await self.db.flush()
        return objs

    # ========== 统一规格管理功能 ==========

    async def get_product_specs_with_options(self, product_id: int) -> List[dict]:
        """获取产品的规格和选项信息"""
        # 查询产品相关的规格组合
        query = select(ProductSpecCombination).options(
            selectinload(ProductSpecCombination.spec_options).selectinload(SpecOption.spec)
        ).where(
            and_(
                ProductSpecCombination.product_id == product_id,
                ProductSpecCombination.is_active == True
            )
        )

        result = await self.db.execute(query)
        combinations = result.scalars().all()

        # 整理规格和选项数据
        specs_map = {}
        for combo in combinations:
            for option in combo.spec_options:
                spec = option.spec
                if spec.id not in specs_map:
                    specs_map[spec.id] = {
                        'id': spec.id,
                        'name': spec.name,
                        'description': spec.description,
                        'sort_order': spec.sort_order,
                        'options': {}
                    }

                if option.id not in specs_map[spec.id]['options']:
                    specs_map[spec.id]['options'][option.id] = {
                        'id': option.id,
                        'value': option.value,
                        'color_code': option.color_code,
                        'image_url': option.image_url,
                        'sort_order': option.sort_order
                    }

        # 转换为列表格式
        specs_list = []
        for spec_data in specs_map.values():
            spec_data['options'] = list(spec_data['options'].values())
            specs_list.append(spec_data)

        # 按排序字段排序
        specs_list.sort(key=lambda x: (x['sort_order'] or 999, x['name']))
        for spec in specs_list:
            spec['options'].sort(key=lambda x: (x['sort_order'] or 999, x['value']))

        return specs_list

    async def get_combinations_with_specs(self, product_id: int, include_inactive: bool = False) -> List[ProductSpecCombination]:
        """获取产品的规格组合及其规格信息"""
        query = select(ProductSpecCombination).options(
            selectinload(ProductSpecCombination.spec_options).selectinload(SpecOption.spec),
            selectinload(ProductSpecCombination.album)
        ).where(ProductSpecCombination.product_id == product_id)

        if not include_inactive:
            query = query.where(ProductSpecCombination.is_active == True)

        query = query.order_by(ProductSpecCombination.sku)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_combination_stats(self, product_id: int) -> dict:
        """获取产品规格组合的统计信息"""
        # 基础统计
        total_query = select(func.count(ProductSpecCombination.id)).where(
            ProductSpecCombination.product_id == product_id
        )
        active_query = select(func.count(ProductSpecCombination.id)).where(
            and_(
                ProductSpecCombination.product_id == product_id,
                ProductSpecCombination.is_active == True
            )
        )

        total_result = await self.db.execute(total_query)
        active_result = await self.db.execute(active_query)

        total_count = total_result.scalar() or 0
        active_count = active_result.scalar() or 0

        # 价格统计
        price_query = select(
            func.min(ProductSpecCombination.price),
            func.max(ProductSpecCombination.price),
            func.avg(ProductSpecCombination.price)
        ).where(
            and_(
                ProductSpecCombination.product_id == product_id,
                ProductSpecCombination.is_active == True,
                ProductSpecCombination.price.isnot(None)
            )
        )

        price_result = await self.db.execute(price_query)
        min_price, max_price, avg_price = price_result.first()

        # 库存统计
        stock_query = select(
            func.sum(ProductSpecCombination.stock_quantity),
            func.count(ProductSpecCombination.id)
        ).where(
            and_(
                ProductSpecCombination.product_id == product_id,
                ProductSpecCombination.is_active == True,
                ProductSpecCombination.stock_quantity > 0
            )
        )

        stock_result = await self.db.execute(stock_query)
        total_stock, in_stock_count = stock_result.first()

        return {
            'total_combinations': total_count,
            'active_combinations': active_count,
            'inactive_combinations': total_count - active_count,
            'min_price': min_price / 100 if min_price else None,  # 转换为元
            'max_price': max_price / 100 if max_price else None,
            'avg_price': float(avg_price) / 100 if avg_price else None,
            'total_stock': int(total_stock) if total_stock else 0,
            'in_stock_combinations': int(in_stock_count) if in_stock_count else 0,
            'out_of_stock_combinations': active_count - (int(in_stock_count) if in_stock_count else 0)
        }

    async def delete_product_combinations(self, product_id: int) -> int:
        """删除产品的所有规格组合"""
        # 先删除关联表数据
        combinations_query = select(ProductSpecCombination.id).where(
            ProductSpecCombination.product_id == product_id
        )
        combinations_result = await self.db.execute(combinations_query)
        combination_ids = [row[0] for row in combinations_result.fetchall()]

        if combination_ids:
            # 删除规格值关联
            delete_relations_query = combination_spec_options.delete().where(
                combination_spec_options.c.combination_id.in_(combination_ids)
            )
            await self.db.execute(delete_relations_query)

            # 删除组合
            delete_combinations_query = select(ProductSpecCombination).where(
                ProductSpecCombination.product_id == product_id
            )
            result = await self.db.execute(delete_combinations_query)
            combinations = result.scalars().all()

            for combo in combinations:
                await self.db.delete(combo)

        return len(combination_ids)

    async def bulk_create_combinations(self, combinations_data: List[dict]) -> List[ProductSpecCombination]:
        """批量创建规格组合"""
        created_combinations = []

        for combo_data in combinations_data:
            spec_option_ids = combo_data.pop('spec_option_ids', [])

            # 创建组合
            combination = ProductSpecCombination(**combo_data)
            self.db.add(combination)
            await self.db.flush()  # 获取ID

            # 添加规格值关联
            if spec_option_ids:
                for option_id in spec_option_ids:
                    await self.db.execute(
                        combination_spec_options.insert().values(
                            combination_id=combination.id,
                            spec_option_id=option_id
                        )
                    )

            created_combinations.append(combination)

        return created_combinations

    async def generate_sku(self, product_id: int, spec_option_ids: List[int]) -> str:
        """生成SKU"""
        # 获取规格值信息
        query = select(SpecOption).options(
            selectinload(SpecOption.spec)
        ).where(SpecOption.id.in_(spec_option_ids))

        result = await self.db.execute(query)
        options = result.scalars().all()

        # 按规格排序，然后按选项排序
        options.sort(key=lambda x: (x.spec.sort_order or 999, x.spec.name, x.sort_order or 999, x.value))

        # 生成SKU：产品ID + 规格值缩写
        sku_parts = [f"P{product_id}"]
        for option in options:
            # 取规格名首字母和选项值首字母
            spec_abbr = ''.join([c for c in option.spec.name if c.isalpha()])[:2].upper()
            value_abbr = ''.join([c for c in option.value if c.isalnum()])[:3].upper()
            sku_parts.append(f"{spec_abbr}{value_abbr}")

        return "-".join(sku_parts)

    async def batch_update_pricing(self, combination_ids: List[int], pricing_data: dict) -> List[ProductSpecCombination]:
        """批量更新价格信息"""
        # 转换价格单位（元转分）
        update_data = {}
        for field, value in pricing_data.items():
            if field in ['price', 'cost_price', 'market_price'] and value is not None:
                update_data[field] = int(value * 100)  # 转换为分
            else:
                update_data[field] = value

        # 使用BaseRepository的批量更新功能
        return await self.batch_update(combination_ids, update_data)