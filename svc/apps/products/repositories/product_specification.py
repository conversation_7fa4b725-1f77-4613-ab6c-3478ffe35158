"""
产品规格统一仓库
整合所有规格相关的数据访问操作
"""
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, func, select, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload

from svc.apps.products.models.product import Product
from svc.apps.products.models.specs import (ProductSpecCombination, Spec,
                                            SpecOption,
                                            combination_spec_options)
from svc.apps.products.schemas.specs import (ProductSpecCombinationCreate,
                                             ProductSpecCombinationUpdate)
from svc.core.repositories import BaseRepository


class ProductSpecificationRepository(BaseRepository[ProductSpecCombination, ProductSpecCombinationCreate, ProductSpecCombinationUpdate]):
    """产品规格统一仓库"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSpecCombination)
    
    async def get_product_specs_with_options(self, product_id: int) -> List[Dict]:
        """获取产品的所有规格及其选项"""
        query = text("""
            SELECT DISTINCT 
                s.id, s.name, s.description, s.sort_order, s.is_active,
                so.id as option_id, so.value, so.color_code, so.image_url, 
                so.sort_order as option_sort, so.is_active as option_active
            FROM specs s
            JOIN spec_options so ON s.id = so.spec_id
            JOIN combination_spec_options cso ON so.id = cso.spec_option_id
            JOIN product_spec_combinations psc ON cso.combination_id = psc.id
            WHERE psc.product_id = :product_id 
                AND s.is_active = true 
                AND so.is_active = true
                AND psc.is_active = true
            ORDER BY s.sort_order, so.sort_order
        """)
        
        result = await self.db.execute(query, {"product_id": product_id})
        rows = result.fetchall()
        
        return self._group_specs_with_options(rows)
    
    def _group_specs_with_options(self, rows) -> List[Dict]:
        """将查询结果按规格分组"""
        specs_dict = {}
        
        for row in rows:
            spec_id = row.id
            if spec_id not in specs_dict:
                specs_dict[spec_id] = {
                    "id": spec_id,
                    "name": row.name,
                    "description": row.description,
                    "sort_order": row.sort_order,
                    "is_active": row.is_active,
                    "options": []
                }
            
            specs_dict[spec_id]["options"].append({
                "id": row.option_id,
                "value": row.value,
                "color_code": row.color_code,
                "image_url": row.image_url,
                "sort_order": row.option_sort,
                "is_active": row.option_active
            })
        
        return list(specs_dict.values())
    
    async def get_combinations_with_specs(
        self, 
        product_id: int, 
        include_inactive: bool = False,
        **filters
    ) -> List[ProductSpecCombination]:
        """获取产品组合及其规格信息"""
        query = select(ProductSpecCombination).options(
            selectinload(ProductSpecCombination.spec_options).selectinload(SpecOption.spec),
            selectinload(ProductSpecCombination.album)
        ).where(ProductSpecCombination.product_id == product_id)
        
        # 应用过滤条件
        if not include_inactive:
            query = query.where(ProductSpecCombination.is_active == True)
        
        if filters.get('min_stock') is not None:
            query = query.where(ProductSpecCombination.stock_quantity >= filters['min_stock'])
        
        if filters.get('has_price') is not None:
            if filters['has_price']:
                query = query.where(ProductSpecCombination.price.isnot(None))
            else:
                query = query.where(ProductSpecCombination.price.is_(None))
        
        # 排序
        query = query.order_by(
            ProductSpecCombination.is_default.desc(),
            ProductSpecCombination.sku
        )
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def bulk_create_combinations(self, combinations_data: List[Dict]) -> List[ProductSpecCombination]:
        """批量创建规格组合"""
        combinations = []
        
        for data in combinations_data:
            spec_option_ids = data.pop('spec_option_ids', [])
            
            # 创建组合对象
            combination = ProductSpecCombination(**data)
            self.db.add(combination)
            await self.db.flush()  # 获取ID
            
            # 添加规格值关联
            if spec_option_ids:
                for option_id in spec_option_ids:
                    # 直接插入关联表
                    await self.db.execute(
                        combination_spec_options.insert().values(
                            combination_id=combination.id,
                            spec_option_id=option_id
                        )
                    )
            
            combinations.append(combination)
        
        await self.db.flush()
        
        # 重新加载关联数据
        for combination in combinations:
            await self.db.refresh(combination)
        
        return combinations
    
    async def update_combination_with_specs(
        self, 
        combination_id: int, 
        update_data: Dict,
        spec_option_ids: Optional[List[int]] = None
    ) -> Optional[ProductSpecCombination]:
        """更新组合及其规格关联"""
        combination = await self.get_by_id(combination_id)
        if not combination:
            return None
        
        # 更新基本信息
        for field, value in update_data.items():
            if hasattr(combination, field):
                setattr(combination, field, value)
        
        # 更新规格值关联
        if spec_option_ids is not None:
            # 删除现有关联
            await self.db.execute(
                combination_spec_options.delete().where(
                    combination_spec_options.c.combination_id == combination_id
                )
            )
            
            # 添加新关联
            if spec_option_ids:
                for option_id in spec_option_ids:
                    await self.db.execute(
                        combination_spec_options.insert().values(
                            combination_id=combination_id,
                            spec_option_id=option_id
                        )
                    )
        
        await self.db.flush()
        await self.db.refresh(combination)
        return combination
    
    async def generate_sku(self, product_id: int, spec_option_ids: List[int]) -> str:
        """生成唯一SKU"""
        # 获取产品信息
        product_query = select(Product).where(Product.id == product_id)
        result = await self.db.execute(product_query)
        product = result.scalars().first()
        
        if not product:
            raise ValueError(f"Product {product_id} not found")
        
        # 获取规格值信息用于生成可读SKU
        options_query = select(SpecOption).options(
            joinedload(SpecOption.spec)
        ).where(SpecOption.id.in_(spec_option_ids)).order_by(SpecOption.spec_id)
        
        result = await self.db.execute(options_query)
        options = result.scalars().all()
        
        # 生成SKU: PRODUCT_CODE-SPEC1-SPEC2-...
        base_sku = product.sku or f"P{product_id}"
        spec_parts = []
        
        for option in options:
            # 取规格值的前3个字符作为SKU部分
            part = option.value[:3].upper().replace(' ', '')
            spec_parts.append(part)
        
        sku = f"{base_sku}-{'-'.join(spec_parts)}"
        
        # 确保唯一性
        counter = 1
        original_sku = sku
        while await self._sku_exists(sku):
            sku = f"{original_sku}-{counter:02d}"
            counter += 1
        
        return sku
    
    async def _sku_exists(self, sku: str) -> bool:
        """检查SKU是否存在"""
        query = select(ProductSpecCombination).where(ProductSpecCombination.sku == sku)
        result = await self.db.execute(query)
        return result.scalars().first() is not None
    
    async def get_combination_stats(self, product_id: int) -> Dict:
        """获取产品规格组合统计信息"""
        query = select(
            func.count(ProductSpecCombination.id).label('total_combinations'),
            func.count(ProductSpecCombination.id).filter(
                ProductSpecCombination.is_active == True
            ).label('active_combinations'),
            func.count(ProductSpecCombination.id).filter(
                ProductSpecCombination.price.isnot(None)
            ).label('priced_combinations'),
            func.sum(ProductSpecCombination.stock_quantity).label('total_stock'),
            func.avg(ProductSpecCombination.price).label('avg_price')
        ).where(ProductSpecCombination.product_id == product_id)
        
        result = await self.db.execute(query)
        row = result.first()
        
        return {
            'total_combinations': row.total_combinations or 0,
            'active_combinations': row.active_combinations or 0,
            'priced_combinations': row.priced_combinations or 0,
            'total_stock': int(row.total_stock or 0),
            'avg_price': float(row.avg_price or 0) / 100 if row.avg_price else 0  # 转换为元
        }
    
    async def delete_product_combinations(self, product_id: int) -> int:
        """删除产品的所有规格组合"""
        # 先删除关联表数据
        combinations_query = select(ProductSpecCombination.id).where(
            ProductSpecCombination.product_id == product_id
        )
        result = await self.db.execute(combinations_query)
        combination_ids = [row[0] for row in result.fetchall()]
        
        if combination_ids:
            # 删除关联表记录
            await self.db.execute(
                combination_spec_options.delete().where(
                    combination_spec_options.c.combination_id.in_(combination_ids)
                )
            )
            
            # 删除组合记录
            deleted_count = await self.db.execute(
                ProductSpecCombination.__table__.delete().where(
                    ProductSpecCombination.product_id == product_id
                )
            )
            
            await self.db.flush()
            return deleted_count.rowcount
        
        return 0

    async def batch_update_combinations(
        self,
        updates: List[Dict[str, Any]]
    ) -> List[ProductSpecCombination]:
        """批量更新规格组合"""
        updated_combinations = []

        for update_data in updates:
            combination_id = update_data.pop('id')
            spec_option_ids = update_data.pop('spec_option_ids', None)

            combination = await self.update_combination_with_specs(
                combination_id, update_data, spec_option_ids
            )

            if combination:
                updated_combinations.append(combination)

        return updated_combinations

    async def batch_update_pricing(
        self,
        combination_ids: List[int],
        pricing_data: Dict[str, Any]
    ) -> List[ProductSpecCombination]:
        """批量更新价格信息"""
        # 转换价格单位（元转分）
        update_data = {}
        for field, value in pricing_data.items():
            if field in ['price', 'cost_price', 'market_price'] and value is not None:
                update_data[field] = int(value * 100)  # 转换为分
            else:
                update_data[field] = value

        updated_combinations = []
        for combination_id in combination_ids:
            combination = await self.update_combination_with_specs(
                combination_id, update_data
            )
            if combination:
                updated_combinations.append(combination)

        return updated_combinations

    async def batch_update_stock(
        self,
        stock_updates: List[Dict[str, Any]]
    ) -> List[ProductSpecCombination]:
        """批量更新库存信息"""
        updated_combinations = []

        for update in stock_updates:
            combination_id = update['combination_id']
            stock_data = {
                'stock_quantity': update.get('stock_quantity'),
                'min_stock_level': update.get('min_stock_level'),
                'max_stock_level': update.get('max_stock_level')
            }
            # 过滤None值
            stock_data = {k: v for k, v in stock_data.items() if v is not None}

            combination = await self.update_combination_with_specs(
                combination_id, stock_data
            )
            if combination:
                updated_combinations.append(combination)

        return updated_combinations

    async def batch_toggle_status(
        self,
        combination_ids: List[int],
        is_active: bool
    ) -> List[ProductSpecCombination]:
        """批量切换组合状态"""
        return await self.batch_update_pricing(
            combination_ids,
            {'is_active': is_active}
        )

    async def find_combinations_by_specs(
        self,
        product_id: int,
        spec_option_ids: List[int]
    ) -> List[ProductSpecCombination]:
        """根据规格值查找组合"""
        # 构建子查询，查找包含所有指定规格值的组合
        subquery = select(combination_spec_options.c.combination_id).where(
            combination_spec_options.c.spec_option_id.in_(spec_option_ids)
        ).group_by(combination_spec_options.c.combination_id).having(
            func.count(combination_spec_options.c.spec_option_id) == len(spec_option_ids)
        )

        # 主查询
        query = select(ProductSpecCombination).options(
            selectinload(ProductSpecCombination.spec_options).selectinload(SpecOption.spec),
            selectinload(ProductSpecCombination.album)
        ).where(
            and_(
                ProductSpecCombination.product_id == product_id,
                ProductSpecCombination.id.in_(subquery),
                ProductSpecCombination.is_active == True
            )
        )

        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_combinations_by_price_range(
        self,
        product_id: int,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None
    ) -> List[ProductSpecCombination]:
        """根据价格范围查找组合"""
        query = select(ProductSpecCombination).options(
            selectinload(ProductSpecCombination.spec_options).selectinload(SpecOption.spec),
            selectinload(ProductSpecCombination.album)
        ).where(
            and_(
                ProductSpecCombination.product_id == product_id,
                ProductSpecCombination.is_active == True
            )
        )

        if min_price is not None:
            query = query.where(ProductSpecCombination.price >= int(min_price * 100))

        if max_price is not None:
            query = query.where(ProductSpecCombination.price <= int(max_price * 100))

        result = await self.db.execute(query)
        return result.scalars().all()
