from typing import Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from svc.apps.albums.schemas.album import AlbumResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse
from svc.core.schemas.batch import BaseBatchUpdateRequest, BatchOperationResult


class SpecBase(CamelCaseModel):
    name: str = Field(..., max_length=100, description="规格名称")
    description: Optional[str] = Field(None, max_length=500, description="规格描述")
    sort_order: int = Field(0, description="排序顺序")
    is_active: bool = Field(True, description="是否启用")

class SpecOptionBase(CamelCaseModel):
    value: str = Field(..., max_length=100, description="规格选项值")
    spec_id: int = Field(..., description="所属规格ID")
    color_code: Optional[str] = Field(None, max_length=7, description="颜色代码(如#FF0000)")
    image_url: Optional[str] = Field(None, max_length=500, description="规格值图片URL")
    sort_order: int = Field(0, description="排序顺序")
    is_active: bool = Field(True, description="是否启用")

class SpecOptionCreate(SpecOptionBase):
    pass

class SpecOptionUpdate(CamelCaseModel):
    value: Optional[str] = Field(None, max_length=100, description="规格选项值")
    color_code: Optional[str] = Field(None, max_length=7, description="颜色代码")
    image_url: Optional[str] = Field(None, max_length=500, description="规格值图片URL")
    sort_order: Optional[int] = Field(None, description="排序顺序")
    is_active: Optional[bool] = Field(None, description="是否启用")

class SpecOptionResponse(SpecOptionBase):
    id: int = Field(..., description="规格选项ID")
    model_config = ConfigDict(
        from_attributes=True,
    )

class SpecCreate(SpecBase):
    options: List[str] = Field(..., description="规格选项值列表")

class SpecUpdate(CamelCaseModel):
    name: Optional[str] = Field(None, max_length=100, description="规格名称")
    description: Optional[str] = Field(None, max_length=500, description="规格描述")
    sort_order: Optional[int] = Field(None, description="排序顺序")
    is_active: Optional[bool] = Field(None, description="是否启用")

class SpecResponse(SpecBase):
    id: int = Field(..., description="规格ID")
    options: List[SpecOptionResponse] = Field(..., description="规格选项列表")
    model_config = ConfigDict(
        from_attributes=True,
    )

# 删除冗余的ProductSpec和ProductSpecOption相关Schema
# 这些功能已经通过ProductSpecCombination实现

class ProductSpecCombinationBase(CamelCaseModel):
    product_id: int = Field(..., description="商品ID")
    sku: str = Field(..., description="SKU")

    # 价格相关字段(前端传入元，后端存储分)
    price: Optional[float] = Field(None, description="价格(元)")
    cost_price: Optional[float] = Field(None, description="成本价(元)")
    market_price: Optional[float] = Field(None, description="市场价(元)")

    # 库存相关字段
    stock_quantity: int = Field(0, description="库存数量")
    min_stock_level: int = Field(0, description="最小库存水平")
    max_stock_level: Optional[int] = Field(None, description="最大库存水平")

    # 状态字段
    is_active: bool = Field(True, description="是否启用")
    is_default: bool = Field(False, description="是否为默认组合")

    # 扩展字段
    weight: Optional[float] = Field(None, description="重量(kg)")
    barcode: Optional[str] = Field(None, description="条码")
    description: Optional[str] = Field(None, description="组合描述")

    # 关联字段
    spec_option_ids: List[int] = Field(..., description="该组合包含的所有规格值ID")
    album_id: Optional[int] = Field(default=None, description="关联图册ID")

class ProductSpecCombinationCreate(ProductSpecCombinationBase):
    pass

class ProductSpecCombinationUpdate(CamelCaseModel):
    price: Optional[float] = Field(None, description="价格(元)")
    cost_price: Optional[float] = Field(None, description="成本价(元)")
    market_price: Optional[float] = Field(None, description="市场价(元)")
    stock_quantity: Optional[int] = Field(None, description="库存数量")
    min_stock_level: Optional[int] = Field(None, description="最小库存水平")
    max_stock_level: Optional[int] = Field(None, description="最大库存水平")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_default: Optional[bool] = Field(None, description="是否为默认组合")
    weight: Optional[float] = Field(None, description="重量(kg)")
    barcode: Optional[str] = Field(None, description="条码")
    description: Optional[str] = Field(None, description="组合描述")

class ProductSpecCombinationResponse(ProductSpecCombinationBase):
    id: int
    album: Optional[AlbumResponse] = Field(default=None, description="关联图册")
    model_config = ConfigDict(
        from_attributes=True,
    )

class SpecListResponse(PaginatedResponse[SpecResponse]):
    """规格分页列表响应模型"""
    pass

class SpecOptionListResponse(PaginatedResponse[SpecOptionResponse]):
    """规格值分页列表响应模型"""
    pass

class ProductSpecCombinationListResponse(PaginatedResponse[ProductSpecCombinationResponse]):
    """规格组合分页列表响应模型"""
    pass

# 新增：一站式设置产品规格的Schema
class SpecWithOptions(CamelCaseModel):
    """规格及其选项定义"""
    name: str = Field(..., description="规格名称")
    description: Optional[str] = Field(None, description="规格描述")
    sort_order: int = Field(0, description="排序顺序")
    options: List[str] = Field(..., description="规格选项值列表")

class ProductSpecificationSetup(CamelCaseModel):
    """产品规格一站式设置"""
    specs: List[SpecWithOptions] = Field(..., description="规格定义列表")
    auto_generate_combinations: bool = Field(True, description="是否自动生成所有组合")
    default_stock_quantity: int = Field(0, description="默认库存数量")

class ProductSpecificationResponse(CamelCaseModel):
    """产品完整规格信息响应"""
    product_id: int = Field(..., description="商品ID")
    specs: List[SpecResponse] = Field(..., description="规格列表")
    combinations: List[ProductSpecCombinationResponse] = Field(..., description="组合列表")
    stats: Dict = Field(..., description="统计信息")

# 使用通用批量更新Schema
class CombinationPricingData(CamelCaseModel):
    """组合价格更新数据"""
    price: Optional[float] = Field(None, description="价格(元)")
    cost_price: Optional[float] = Field(None, description="成本价(元)")
    market_price: Optional[float] = Field(None, description="市场价(元)")

class CombinationPricingUpdate(BaseBatchUpdateRequest[CombinationPricingData]):
    """批量更新组合价格"""
    pass

class SpecificationCopyOptions(CamelCaseModel):
    """规格复制选项"""
    copy_pricing: bool = Field(False, description="是否复制价格信息")
    copy_stock: bool = Field(False, description="是否复制库存信息")
    copy_images: bool = Field(False, description="是否复制图片信息")