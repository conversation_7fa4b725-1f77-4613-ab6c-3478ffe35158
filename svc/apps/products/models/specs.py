from sqlalchemy import (JSON, BigInteger, Boolean, Column, Float, Foreign<PERSON>ey,
                        Integer, String, Table, Text, UniqueConstraint)
from sqlalchemy.orm import relationship

from svc.apps.albums.models.album import Album
from svc.core.models.base import Base

# 规格组合与规格值关联表
combination_spec_options = Table(
    'combination_spec_options',
    Base.metadata,
    Column('combination_id', BigInteger, ForeignKey('product_spec_combinations.id'), primary_key=True),
    Column('spec_option_id', BigInteger, ForeignKey('spec_options.id'), primary_key=True)
)


class Spec(Base):
    """
    规格定义模型，如"颜色"、"尺寸"
    """
    __tablename__ = "specs"
    __table_args__ = (UniqueConstraint("name", name="uq_spec_name"),)
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="规格名称")
    description = Column(String(500), nullable=True, comment="规格描述")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    is_active = Column(Boolean, default=True, comment="是否启用")

    # 关系
    options = relationship("SpecOption", back_populates="spec", cascade="all, delete-orphan", lazy="selectin")

class SpecOption(Base):
    """
    规格值模型，如"红色"、"XL"
    """
    __tablename__ = "spec_options"
    __table_args__ = (UniqueConstraint("spec_id", "value", name="uq_specoption_specid_value"),)
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    spec_id = Column(BigInteger, ForeignKey("specs.id"), nullable=False)
    value = Column(String(100), nullable=False, comment="规格值")
    color_code = Column(String(7), nullable=True, comment="颜色代码(如#FF0000)")
    image_url = Column(String(500), nullable=True, comment="规格值图片URL")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    is_active = Column(Boolean, default=True, comment="是否启用")

    # 关系
    spec = relationship("Spec", back_populates="options")

# 删除冗余的ProductSpec和ProductSpecOption类
# 这些类的功能已经通过combination_spec_options关联表和ProductSpecCombination实现

class ProductSpecCombination(Base):
    """
    商品规格组合（可售单元），如"红色-XL"
    重构后的模型，使用关联表而非JSON存储规格值ID
    """
    __tablename__ = "product_spec_combinations"
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    product_id = Column(BigInteger, ForeignKey("products.id"), nullable=False)
    sku = Column(String(100), nullable=False, unique=True, index=True, comment="SKU")

    # 价格相关字段(使用分为单位)
    price = Column(Integer, nullable=True, comment="价格(分)")
    cost_price = Column(Integer, nullable=True, comment="成本价(分)")
    market_price = Column(Integer, nullable=True, comment="市场价(分)")

    # 库存相关字段
    stock_quantity = Column(Integer, default=0, comment="库存数量")
    min_stock_level = Column(Integer, default=0, comment="最小库存水平")
    max_stock_level = Column(Integer, nullable=True, comment="最大库存水平")

    # 状态字段
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_default = Column(Boolean, default=False, comment="是否为默认组合")

    # 扩展字段
    weight = Column(Float, nullable=True, comment="重量(kg)")
    barcode = Column(String(100), nullable=True, index=True, comment="条码")
    description = Column(Text, nullable=True, comment="组合描述")

    # 关联字段
    album_id = Column(BigInteger, ForeignKey("albums.id"), nullable=True, comment="关联图册ID")

    # 关系
    album = relationship("Album", lazy="selectin", uselist=False, foreign_keys=[album_id])
    spec_options = relationship("SpecOption", secondary=combination_spec_options, lazy="selectin")

    def __repr__(self):
        return f"<ProductSpecCombination(id={self.id}, sku='{self.sku}', product_id={self.product_id})>"