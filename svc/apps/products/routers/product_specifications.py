"""
产品规格统一API路由
重构后的简化API设计，提供一站式服务
"""
from typing import List, Optional

from fastapi import APIRouter, Body, Depends, Path, Query, status

from svc.apps.auth.dependencies import get_current_active_user, has_permission
from svc.apps.auth.models.user import User
from svc.apps.products.dependencies import get_product_spec_combination_service
from svc.apps.products.schemas.specs import (CombinationPricingUpdate,
                                             ProductSpecCombinationResponse,
                                             ProductSpecificationResponse,
                                             ProductSpecificationSetup,
                                             SpecificationCopyOptions,
                                             SpecWithOptions)
from svc.apps.products.services.specs import ProductSpecCombinationService
from svc.core.exceptions import handle_route_errors
from svc.core.schemas.base import PageParams
from svc.core.schemas.batch import BatchOperationResult
from svc.core.services.result import Result

router = APIRouter(tags=["产品规格管理"], prefix="/products")


@router.post(
    "/{product_id}/specifications", 
    response_model=Result[List[ProductSpecCombinationResponse]], 
    summary="一站式设置产品规格",
    description="""
    一站式设置产品规格，支持：
    - 创建规格和规格值
    - 自动生成所有组合
    - 批量设置基础信息
    
    该接口会清空现有规格组合，重新生成所有组合。
    """
)
@handle_route_errors()
async def setup_product_specifications(
    product_id: int = Path(..., description="产品ID"),
    setup_data: ProductSpecificationSetup = Body(..., description="规格设置数据"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:update"))
):
    """一站式设置产品规格"""
    return await service.setup_product_specifications(
        product_id, 
        setup_data.specs, 
        current_user.id
    )


@router.get(
    "/{product_id}/specifications",
    response_model=Result[ProductSpecificationResponse],
    summary="获取产品完整规格信息",
    description="""
    获取产品的完整规格信息，包括：
    - 规格定义和选项
    - 所有规格组合
    - 统计信息
    """
)
@handle_route_errors()
async def get_product_specifications(
    product_id: int = Path(..., description="产品ID"),
    include_inactive: bool = Query(False, description="是否包含已停用的组合"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service)
):
    """获取产品的完整规格信息"""
    return await service.get_product_specifications(product_id, include_inactive)


@router.put(
    "/{product_id}/combinations/pricing",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="批量更新组合价格",
    description="""
    批量更新规格组合的价格信息，支持：
    - 销售价格
    - 成本价格
    - 市场价格
    """
)
@handle_route_errors()
async def batch_update_combination_pricing(
    product_id: int = Path(..., description="产品ID"),
    pricing_update: CombinationPricingUpdate = Body(..., description="价格更新数据"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:update"))
):
    """批量更新规格组合的价格信息"""
    return await service.update_combination_pricing(
        pricing_update.resource_ids,
        pricing_update.update_data.model_dump(exclude_none=True),
        current_user.id
    )


@router.post(
    "/{target_product_id}/specifications/copy/{source_product_id}",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="复制规格配置",
    description="""
    从源产品复制规格配置到目标产品，支持选择性复制：
    - 规格和规格值（必选）
    - 价格信息（可选）
    - 库存信息（可选）
    - 图片信息（可选）
    """
)
@handle_route_errors()
async def copy_product_specifications(
    source_product_id: int = Path(..., description="源产品ID"),
    target_product_id: int = Path(..., description="目标产品ID"),
    copy_options: SpecificationCopyOptions = Body(..., description="复制选项"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:update"))
):
    """从源产品复制规格配置到目标产品"""
    # 注意：复制功能需要在ProductSpecCombinationService中实现
    return Result.error(501, "复制功能暂未实现")


@router.get(
    "/{product_id}/combinations/search",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="根据规格值查找组合",
    description="根据指定的规格值ID列表查找匹配的组合"
)
@handle_route_errors()
async def find_combinations_by_specs(
    product_id: int = Path(..., description="产品ID"),
    spec_option_ids: List[int] = Query(..., description="规格值ID列表"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service)
):
    """根据规格值查找组合"""
    # 注意：此功能需要在ProductSpecCombinationService中实现
    return Result.error(501, "按规格值查找功能暂未实现")


@router.get(
    "/{product_id}/combinations/price-range",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="根据价格范围查找组合",
    description="根据价格范围查找符合条件的组合"
)
@handle_route_errors()
async def get_combinations_by_price_range(
    product_id: int = Path(..., description="产品ID"),
    min_price: Optional[float] = Query(None, description="最低价格(元)"),
    max_price: Optional[float] = Query(None, description="最高价格(元)"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service)
):
    """根据价格范围查找组合"""
    # 注意：此功能需要在ProductSpecCombinationService中实现
    return Result.error(501, "按价格范围查找功能暂未实现")


# 兼容性接口 - 保持与现有系统的兼容性
@router.post(
    "/{product_id}/specifications/quick-setup",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="快速设置产品规格",
    description="""
    快速设置产品规格的简化接口，适用于简单场景：
    - 只需要提供规格名称和选项值
    - 自动生成所有组合
    - 使用默认设置
    """
)
@handle_route_errors()
async def quick_setup_specifications(
    product_id: int = Path(..., description="产品ID"),
    specs: List[SpecWithOptions] = Body(..., description="规格列表"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:update"))
):
    """快速设置产品规格"""
    setup_data = ProductSpecificationSetup(
        specs=specs,
        auto_generate_combinations=True,
        default_stock_quantity=0
    )
    return await service.setup_product_specifications(
        product_id, 
        setup_data.specs, 
        current_user.id
    )


@router.delete(
    "/{product_id}/specifications",
    response_model=Result[bool],
    summary="清空产品规格",
    description="删除产品的所有规格组合，但保留规格定义"
)
@handle_route_errors()
async def clear_product_specifications(
    product_id: int = Path(..., description="产品ID"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:delete"))
):
    """清空产品规格组合"""
    # 注意：此功能需要在ProductSpecCombinationService中实现
    return Result.error(501, "清空规格组合功能暂未实现")


@router.get(
    "/{product_id}/specifications/stats",
    response_model=Result[dict],
    summary="获取产品规格统计信息",
    description="获取产品规格的统计信息，包括组合数量、价格分布等"
)
@handle_route_errors()
async def get_specification_stats(
    product_id: int = Path(..., description="产品ID"),
    service: ProductSpecCombinationService = Depends(get_product_spec_combination_service)
):
    """获取产品规格统计信息"""
    # 注意：此功能需要在ProductSpecCombinationService中实现
    return Result.error(501, "获取统计信息功能暂未实现")
