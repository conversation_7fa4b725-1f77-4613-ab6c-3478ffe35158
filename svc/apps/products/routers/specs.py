from typing import List

from fastapi import (APIRouter, Body, Depends, HTTPException, Path, Query,
                     status)

from svc.apps.auth.dependencies import get_current_active_user, has_permission
from svc.apps.auth.models.user import User
from svc.apps.products.dependencies import (
    get_product_spec_combination_service, get_spec_option_service,
    get_spec_service)
from svc.apps.products.schemas.specs import (  # 统一规格管理相关Schema
    CombinationPricingUpdate, ProductSpecCombinationCreate,
    ProductSpecCombinationListResponse, ProductSpecCombinationResponse,
    ProductSpecCombinationUpdate, ProductSpecificationResponse,
    ProductSpecificationSetup, SpecCreate, SpecificationCopyOptions,
    SpecListResponse, SpecOptionCreate, SpecOptionListResponse,
    SpecOptionResponse, SpecOptionUpdate, SpecResponse, SpecUpdate,
    SpecWithOptions)
from svc.core.exceptions import handle_route_errors
from svc.core.schemas.base import PageParams
from svc.core.services.result import Result

# 假设已实现依赖注入和服务实例获取
# from svc.apps.products.dependencies import get_spec_service, get_spec_option_service, get_product_spec_combination_service

router = APIRouter(tags=["商品规格管理"])

# 规格相关接口
@router.post("/product/{product_id}/specs", response_model=Result[SpecResponse], summary="新增商品规格")
@handle_route_errors()
async def create_spec(
    product_id: int,
    spec: SpecCreate,
    service=Depends(get_spec_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("spec:create"))
) -> Result[SpecResponse]:
    """
    新增商品规格（需认证和spec:create权限）
    """
    return await service.create_spec(spec)

@router.get("/product/{product_id}/specs", response_model=Result[SpecListResponse], summary="获取商品规格及规格值（分页）")
@handle_route_errors()
async def list_specs(
    product_id: int,
    params: PageParams = Depends(),
    service=Depends(get_spec_service)
) -> Result[SpecListResponse]:
    """
    获取商品规格及规格值（分页）
    """
    return await service.list_specs(product_id, params)

# 规格值相关接口
@router.post("/product/{product_id}/specs/{spec_id}/options", response_model=Result[SpecOptionResponse], summary="新增商品规格值")
@handle_route_errors()
async def create_spec_option(
    product_id: int,
    spec_id: int,
    option: SpecOptionCreate,
    service=Depends(get_spec_option_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("spec:create"))
) -> Result[SpecOptionResponse]:
    """
    新增商品规格值（需认证和spec:create权限）
    """
    return await service.create_option(option)

@router.get("/product/{product_id}/specs/{spec_id}/options", response_model=Result[SpecOptionListResponse], summary="获取规格值列表（分页）")
@handle_route_errors()
async def list_spec_options(
    product_id: int,
    spec_id: int,
    params: PageParams = Depends(),
    service=Depends(get_spec_option_service)
) -> Result[SpecOptionListResponse]:
    """
    获取指定规格的规格值列表（分页）
    """
    return await service.list_options(spec_id, params)

# 规格组合相关接口
@router.post("/product/{product_id}/combinations/generate", response_model=Result[List[ProductSpecCombinationResponse]], summary="生成商品所有规格组合")
@handle_route_errors()
async def generate_combinations(
    product_id: int,
    service=Depends(get_product_spec_combination_service),
    spec_service=Depends(get_spec_service),
    spec_option_service=Depends(get_spec_option_service)
) -> Result[List[ProductSpecCombinationResponse]]:
    """
    生成商品所有规格组合
    只负责参数校验、依赖注入、调用Service，返回标准响应
    """
    return await service.generate_and_save_combinations(product_id, spec_service, spec_option_service)

@router.get("/product/{product_id}/combinations", response_model=Result[ProductSpecCombinationListResponse], summary="获取商品所有规格组合（分页）")
@handle_route_errors()
async def list_combinations(
    product_id: int,
    params: PageParams = Depends(),
    service=Depends(get_product_spec_combination_service)
) -> Result[ProductSpecCombinationListResponse]:
    """
    获取商品所有规格组合（分页）
    """
    return await service.list_combinations(product_id, params)

@router.put("/product/{product_id}/combinations/{combination_id}", response_model=Result[ProductSpecCombinationResponse], summary="更新规格组合")
@handle_route_errors()
async def update_combination(
    product_id: int,
    combination_id: int,
    update: ProductSpecCombinationUpdate,
    service=Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("spec:update"))
) -> Result[ProductSpecCombinationResponse]:
    """
    更新规格组合（需认证和spec:update权限）
    """
    return await service.update_combination(combination_id, update.model_dump(exclude_unset=True))


# ========== 统一规格管理接口 ==========

@router.post(
    "/products/{product_id}/specifications",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="一站式设置产品规格",
    description="""
    一站式设置产品规格，包括：
    1. 创建或获取规格和规格值
    2. 生成所有可能的规格组合
    3. 自动生成SKU
    4. 支持批量操作
    """
)
@handle_route_errors()
async def setup_product_specifications(
    product_id: int = Path(..., description="产品ID"),
    setup_data: ProductSpecificationSetup = Body(..., description="规格设置数据"),
    service=Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:update"))
):
    """一站式设置产品规格"""
    return await service.setup_product_specifications(
        product_id,
        setup_data.specs,
        current_user.id
    )


@router.get(
    "/products/{product_id}/specifications",
    response_model=Result[ProductSpecificationResponse],
    summary="获取产品完整规格信息",
    description="获取产品的完整规格信息，包括规格、选项、组合和统计数据"
)
@handle_route_errors()
async def get_product_specifications(
    product_id: int = Path(..., description="产品ID"),
    include_inactive: bool = Query(False, description="是否包含已停用的组合"),
    service=Depends(get_product_spec_combination_service)
):
    """获取产品的完整规格信息"""
    return await service.get_product_specifications(product_id, include_inactive)


@router.put(
    "/products/{product_id}/combinations/pricing",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="批量更新组合价格",
    description="""
    批量更新规格组合的价格信息，支持：
    - 销售价格
    - 成本价格
    - 市场价格
    """
)
@handle_route_errors()
async def batch_update_combination_pricing(
    product_id: int = Path(..., description="产品ID"),
    pricing_update: CombinationPricingUpdate = Body(..., description="价格更新数据"),
    service=Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:update"))
):
    """批量更新规格组合的价格信息"""
    return await service.update_combination_pricing(
        pricing_update.resource_ids,
        pricing_update.update_data.model_dump(exclude_none=True),
        current_user.id
    )


@router.post(
    "/products/{target_product_id}/specifications/copy/{source_product_id}",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="复制规格配置",
    description="从源产品复制规格配置到目标产品，支持选择性复制价格和库存信息"
)
@handle_route_errors()
async def copy_product_specifications(
    source_product_id: int = Path(..., description="源产品ID"),
    target_product_id: int = Path(..., description="目标产品ID"),
    copy_options: SpecificationCopyOptions = Body(..., description="复制选项"),
    service=Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:update"))
):
    """从源产品复制规格配置到目标产品"""
    # 注意：复制功能需要在ProductSpecCombinationService中实现
    return Result.error(501, "复制功能暂未实现")


@router.post(
    "/products/{product_id}/specifications/quick-setup",
    response_model=Result[List[ProductSpecCombinationResponse]],
    summary="快速设置产品规格",
    description="快速设置产品规格的简化接口，直接传入规格列表"
)
@handle_route_errors()
async def quick_setup_specifications(
    product_id: int = Path(..., description="产品ID"),
    specs: List[SpecWithOptions] = Body(..., description="规格列表"),
    service=Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:update"))
):
    """快速设置产品规格"""
    return await service.setup_product_specifications(
        product_id,
        specs,
        current_user.id
    )