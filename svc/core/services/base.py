"""
服务层基类模块。
提供服务层抽象基类和通用功能。

本模块提供了一个服务层的抽象基类，包含错误处理、结果创建和缓存管理等通用功能。
所有的服务类都应该继承自BaseService类，并实现必要的方法。

缓存使用指南:
- 所有需要缓存功能的服务类应该在构造函数中传入redis参数
- 使用cache_resource方法缓存资源数据，可以自动处理各种类型的数据
- 使用get_cached_resource方法获取缓存的资源数据，提供反序列化函数以将JSON转回对象
- 使用delete_cache方法删除一个或多个缓存键
- 子类可以定义自己的辅助方法来生成标准化的缓存键

示例:
```python
class MyService(BaseService[MyModel, MyResult]):
    def _get_my_model_cache_key(self, model_id: int) -> str:
        return f"my_model:{model_id}"
        
    async def get_resource_by_id(self, model_id: int) -> Optional[MyModel]:
        # 先尝试从缓存获取
        cache_key = self._get_my_model_cache_key(model_id)
        cached_model = await self.get_cached_resource(
            cache_key,
            lambda data: MyModel.from_dict(data)
        )
        if cached_model:
            return cached_model
        
        # 如果找到模型，则缓存它
        if model:
            await self.cache_resource(cache_key, model)
            
        return model
```
"""
import json
import logging
from datetime import datetime
from functools import wraps
from typing import (Any, Callable, Dict, Generic, List, Optional, Type,
                    TypeVar, Union, cast)

from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services.result import Result, ResultFactory

logger = logging.getLogger(__name__)

# 定义泛型类型变量用于模型和结果
ModelType = TypeVar("ModelType")
ResultType = TypeVar("ResultType", bound=Result)

# 缓存配置
CACHE_VERSION = "v1"  # 缓存版本号
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）
CACHE_TTL_SHORT = 300  # 短期缓存过期时间（5分钟）
CACHE_TTL_LONG = 86400  # 长期缓存过期时间（24小时）


class BaseService(Generic[ModelType, ResultType]):
    """服务基类，提供通用功能和错误处理"""

    # 资源类型名称，子类应该覆盖此属性
    resource_type: str = "资源"
    
    # 结果工厂，用于创建结果对象
    result_factory: Type[ResultFactory] = ResultFactory
    
    def __init__(self, redis: Optional[Redis] = None):
        """初始化服务基类
        
        Args:
            db: 数据库会话
            redis: Redis客户端（可选）
        """
        self.redis = redis
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
    
    def _get_resource_cache_key(self, resource_id: int) -> str:
        """获取资源缓存键
        
        Args:
            resource_id: 资源ID
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:{CACHE_VERSION}:{resource_id}"
    
    def _get_collection_cache_key(self, collection_name: str, **filters) -> str:
        """获取集合缓存键
        
        Args:
            collection_name: 集合名称
            **filters: 过滤条件
            
        Returns:
            str: 缓存键
        """
        filter_str = ":".join(f"{k}={v}" for k, v in sorted(filters.items()))
        return f"{self.resource_type}:{CACHE_VERSION}:collection:{collection_name}:{filter_str}"
    
    def _get_cache_stats_key(self) -> str:
        """获取缓存统计信息的键
        
        Returns:
            str: 缓存统计键
        """
        return f"{self.resource_type}:{CACHE_VERSION}:stats"
    
    async def _increment_cache_stats(self, stat_type: str) -> None:
        """增加缓存统计计数
        
        Args:
            stat_type: 统计类型（hit/miss/error）
        """
        if not self.redis:
            return
            
        try:
            stats_key = self._get_cache_stats_key()
            # 由于现在使用异步Redis客户端，直接await操作
            try:
                await self.redis.hincrby(stats_key, stat_type, 1)
            except Exception as e:
                self.logger.debug(f"增加缓存统计时出错: {str(e)}")
        except Exception as e:
            self.logger.warning(f"更新缓存统计失败: stat_type={stat_type}, 错误={str(e)}")
    
    def create_error_result(
        self,
        error_code: int,
        error_message: str,
        data: Optional[Any] = None
    ) -> ResultType:
        """创建错误结果
        
        Args:
            error_code: 错误代码
            error_message: 错误消息
            data: 附加数据
            
        Returns:
            结果对象
        """
        self.logger.debug(f"创建错误结果: error_code={error_code}, error_message={error_message}")
        return cast(
            ResultType,
            self.result_factory.error(error_code, error_message, data)
        )
    
    def create_success_result(self, data: Optional[Any] = None) -> ResultType:
        """创建成功结果
        
        Args:
            data: 返回数据
            
        Returns:
            结果对象
        """
        return cast(
            ResultType,
            self.result_factory.success(data)
        )
    
    def resource_not_found_result(
        self,
        resource_id: Union[str, int],
        result_code: int = ErrorCode.NOT_FOUND
    ) -> ResultType:
        """资源不存在结果
        
        Args:
            resource_id: 资源ID
            result_code: 错误代码
            
        Returns:
            结果对象
        """
        self.logger.debug(f"{self.resource_type}不存在: ID={resource_id}")
        return cast(
            ResultType,
            self.result_factory.resource_not_found(
                resource_type=self.resource_type,
                resource_id=resource_id,
                result_code=result_code
            )
        )
    
    def permission_denied_result(
        self,
        resource_id: Optional[Union[str, int]] = None,
        result_code: int = ErrorCode.PERMISSION_DENIED
    ) -> ResultType:
        """权限不足结果
        
        Args:
            resource_id: 资源ID
            result_code: 错误代码
            
        Returns:
            结果对象
        """
        self.logger.debug(
            f"权限不足: 资源类型={self.resource_type}, ID={resource_id}"
        )
        return cast(
            ResultType,
            self.result_factory.permission_denied(
                resource_type=self.resource_type,
                resource_id=resource_id,
                result_code=result_code
            )
        )
        
    async def cache_resource(self, key: str, resource: Any, expire: int = CACHE_TTL) -> None:
        """缓存资源
        
        Args:
            key: 缓存键
            resource: 要缓存的资源
            expire: 过期时间（秒）
        """
        if not self.redis:
            return
            
        try:
            # 如果资源有to_dict方法，使用它序列化
            if hasattr(resource, 'to_dict'):
                data = resource.to_dict()
            # 如果资源有model_dump方法（Pydantic模型），使用它序列化
            elif hasattr(resource, 'model_dump'):
                data = resource.model_dump()
            # 否则直接使用资源对象
            else:
                data = resource
                
            # 由于现在使用异步Redis客户端，直接await操作
            try:
                await self.redis.set(key, str(data), ex=expire)
            except Exception as e:
                self.logger.debug(f"缓存资源时出错: key={key}, 错误={str(e)}")
            
            self.logger.debug(f"资源缓存成功: {key}")
        except Exception as e:
            await self._increment_cache_stats("error")
            self.logger.warning(f"缓存资源失败: key={key}, 错误={str(e)}")
    
    async def get_cached_resource(
        self,
        key: str,
        deserializer: Callable[[Dict[str, Any]], ModelType]
    ) -> Optional[ModelType]:
        """获取缓存的资源
        
        Args:
            key: 缓存键
            deserializer: 反序列化函数
            
        Returns:
            Optional[ModelType]: 缓存的资源，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            # 由于现在使用异步Redis客户端，直接await操作
            try:
                data = await self.redis.get(key)
                
                if data:
                    await self._increment_cache_stats("hit")
                    return deserializer(json.loads(data))
                else:
                    await self._increment_cache_stats("miss")
                    return None
            except Exception as e:
                self.logger.debug(f"获取缓存资源时出错: key={key}, 错误={str(e)}")
                return None
        except Exception as e:
            await self._increment_cache_stats("error")
            self.logger.warning(f"获取缓存资源失败: key={key}, 错误={str(e)}")
            return None
    
    async def delete_cache(self, key: str) -> None:
        """删除缓存
        
        Args:
            key: 缓存键
        """
        if not self.redis:
            return
            
        try:
            await self.redis.delete(key)
            self.logger.debug(f"缓存删除成功: {key}")
        except Exception as e:
            self.logger.warning(f"删除缓存失败: key={key}, 错误={str(e)}")
    
    async def cache_collection(
        self,
        collection_name: str,
        items: List[ModelType],
        filters: Dict[str, Any] = None,
        expire: int = CACHE_TTL
    ) -> None:
        """缓存集合
        
        Args:
            collection_name: 集合名称
            items: 要缓存的项目列表
            filters: 过滤条件
            expire: 过期时间（秒）
        """
        if not self.redis:
            return
            
        try:
            key = self._get_collection_cache_key(collection_name, **(filters or {}))
            # 序列化每个项目
            serialized_items = []
            for item in items:
                if hasattr(item, 'to_dict'):
                    serialized_items.append(item.to_dict())
                elif hasattr(item, 'model_dump'):
                    serialized_items.append(item.model_dump())
                else:
                    serialized_items.append(item)
                    
            await self.redis.set(key, str(serialized_items), ex=expire)
            self.logger.debug(f"集合缓存成功: {key}, count={len(items)}")
        except Exception as e:
            await self._increment_cache_stats("error")
            self.logger.warning(f"缓存集合失败: name={collection_name}, 错误={str(e)}")
    
    async def get_cached_collection(
        self,
        collection_name: str,
        deserializer: Callable[[List[Dict[str, Any]]], List[ModelType]],
        filters: Dict[str, Any] = None
    ) -> Optional[List[ModelType]]:
        """获取缓存的集合
        
        Args:
            collection_name: 集合名称
            deserializer: 反序列化函数
            filters: 过滤条件
            
        Returns:
            Optional[List[ModelType]]: 缓存的集合，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            key = self._get_collection_cache_key(collection_name, **(filters or {}))
            data = await self.redis.get(key)
            if data:
                await self._increment_cache_stats("hit")
                return deserializer(json.loads(data))
            else:
                await self._increment_cache_stats("miss")
                return None
        except Exception as e:
            await self._increment_cache_stats("error")
            self.logger.warning(f"获取缓存集合失败: name={collection_name}, 错误={str(e)}")
            return None
    
    async def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, int]: 缓存统计数据
        """
        if not self.redis:
            return {"hit": 0, "miss": 0, "error": 0}
            
        try:
            stats_key = self._get_cache_stats_key()
            stats = await self.redis.hgetall(stats_key)
            return {
                "hit": int(stats.get(b"hit", 0)),
                "miss": int(stats.get(b"miss", 0)), 
                "error": int(stats.get(b"error", 0))
            }
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: 错误={str(e)}", exc_info=True)
            return {"hit": 0, "miss": 0, "error": 0}
    
    async def warm_up_cache(self, **filters) -> None:
        """预热缓存
        
        Args:
            **filters: 过滤条件
        """
        raise NotImplementedError("子类需要实现此方法")
    
    async def get_resource_by_id(self, resource_id: Union[str, int]) -> Optional[ModelType]:
        """获取指定ID的资源，子类应该覆盖此方法
        
        Args:
            resource_id: 资源ID
            
        Returns:
            Optional[ModelType]: 资源对象，不存在时返回None
        """
        raise NotImplementedError("子类必须实现get_resource_by_id方法")


# 装饰器函数
def check_resource_exists(id_param: str = "id", error_code: int = ErrorCode.NOT_FOUND):
    """检查资源是否存在的装饰器
    
    Args:
        id_param: ID参数名
        error_code: 错误代码
        
    Returns:
        装饰器函数
    """
    def decorator(method):
        @wraps(method)
        async def wrapper(self: BaseService, *args, **kwargs):
            # 获取资源ID
            resource_id = kwargs.get(id_param)
            if not resource_id:
                return self.create_error_result(
                    result_code=ErrorCode.INVALID_PARAMS,
                    result_msg=f"缺少参数: {id_param}"
                )
            
            # 尝试获取资源
            try:
                resource = await self.get_resource_by_id(resource_id)
                if not resource:
                    return self.resource_not_found_result(resource_id, error_code)
                
                # 将资源对象添加到关键字参数中
                kwargs["resource"] = resource
                return await method(self, *args, **kwargs)
            except Exception as e:
                self.logger.exception(f"检查资源存在时出错: {str(e)}")
                return self.create_error_result(
                    result_code=ErrorCode.INTERNAL_ERROR,
                    result_msg=str(e)
                )
        return wrapper
    return decorator


def handle_service_errors(error_code: int = ErrorCode.OPERATION_FAILED):
    """处理服务方法错误的装饰器
    
    Args:
        error_code: 错误代码
        
    Returns:
        装饰器函数
    """
    def decorator(method):
        @wraps(method)
        async def wrapper(self: BaseService, *args, **kwargs):
            try:
                return await method(self, *args, **kwargs)
            except Exception as e:
                self.logger.exception(f"服务方法执行出错: {str(e)}")
                return self.create_error_result(
                    result_code=error_code,
                    result_msg=str(e)
                )
        return wrapper
    return decorator


class BatchUpdateMixin(Generic[ModelType, ResultType]):
    """批量更新功能混入类，为服务类提供通用批量操作功能"""

    def __init__(self):
        """初始化批量更新混入"""
        # 确保继承类有必要的属性
        if not hasattr(self, 'logger'):
            import logging
            self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    async def batch_update_resources(
        self,
        resource_ids: List[int],
        update_data: Dict[str, Any],
        repository: Any,
        resource_type: str = "资源",
        event_prefix: Optional[str] = None,
        cache_key_generator: Optional[Callable[[int], str]] = None
    ) -> Result:
        """通用批量更新资源方法

        Args:
            resource_ids: 资源ID列表
            update_data: 更新数据字典
            repository: 仓储实例
            resource_type: 资源类型名称
            event_prefix: 事件前缀，如"products:product"
            cache_key_generator: 缓存键生成函数

        Returns:
            Result: 批量更新结果
        """
        try:
            from fastapi_events.dispatcher import dispatch

            from svc.core.schemas.batch import BatchUpdateResponse

            self.logger.info(f"批量更新{resource_type}: 请求更新{len(resource_ids)}个{resource_type}")

            # 验证资源ID列表不为空
            if not resource_ids:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_PARAMS,
                    error_message=f"{resource_type}ID列表不能为空"
                )

            # 验证更新数据不为空
            if not update_data:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_PARAMS,
                    error_message="更新数据不能为空"
                )

            # 检查哪些资源ID存在
            existing_ids = await repository.get_existing_ids(resource_ids)
            if not existing_ids:
                self.logger.warning(f"没有找到任何有效的{resource_type}ID: {resource_ids}")
                response = BatchUpdateResponse(
                    updated_count=0,
                    failed_ids=resource_ids,
                    total_requested=len(resource_ids)
                )
                return self.create_success_result(response)

            # 计算失败的ID
            failed_ids = [rid for rid in resource_ids if rid not in existing_ids]

            # 执行批量更新
            updated_count = await repository.batch_update(existing_ids, update_data)

            # 清除相关缓存
            if cache_key_generator and hasattr(self, 'redis') and self.redis:
                for resource_id in existing_ids:
                    cache_key = cache_key_generator(resource_id)
                    await self.redis.delete(cache_key)

            # 触发批量更新事件
            if event_prefix:
                dispatch(f"{event_prefix}:batch_updated", payload={
                    "updated_ids": existing_ids,
                    "update_data": update_data,
                    "updated_count": updated_count
                })

            response = BatchUpdateResponse(
                updated_count=updated_count,
                failed_ids=failed_ids,
                total_requested=len(resource_ids)
            )

            self.logger.info(f"批量更新{resource_type}完成: 成功更新{updated_count}个{resource_type}，失败{len(failed_ids)}个")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"批量更新{resource_type}失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.UPDATE_FAILED,
                error_message=f"批量更新{resource_type}失败: {str(e)}"
            )

    async def batch_delete_resources(
        self,
        resource_ids: List[int],
        repository: Any,
        resource_type: str = "资源",
        soft_delete: bool = True,
        event_prefix: Optional[str] = None,
        cache_key_generator: Optional[Callable[[int], str]] = None
    ) -> Result:
        """通用批量删除资源方法

        Args:
            resource_ids: 资源ID列表
            repository: 仓储实例
            resource_type: 资源类型名称
            soft_delete: 是否软删除
            event_prefix: 事件前缀
            cache_key_generator: 缓存键生成函数

        Returns:
            Result: 批量删除结果
        """
        try:
            from fastapi_events.dispatcher import dispatch

            from svc.core.schemas.batch import BatchDeleteResponse

            self.logger.info(f"批量删除{resource_type}: 请求删除{len(resource_ids)}个{resource_type}")

            # 验证资源ID列表不为空
            if not resource_ids:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_PARAMS,
                    error_message=f"{resource_type}ID列表不能为空"
                )

            # 检查哪些资源ID存在
            existing_ids = await repository.get_existing_ids(resource_ids)
            if not existing_ids:
                self.logger.warning(f"没有找到任何有效的{resource_type}ID: {resource_ids}")
                response = BatchDeleteResponse(
                    deleted_count=0,
                    failed_ids=resource_ids,
                    total_requested=len(resource_ids)
                )
                return self.create_success_result(response)

            # 计算失败的ID
            failed_ids = [rid for rid in resource_ids if rid not in existing_ids]

            # 执行批量删除
            deleted_count = await repository.batch_delete(existing_ids, soft_delete=soft_delete)

            # 清除相关缓存
            if cache_key_generator and hasattr(self, 'redis') and self.redis:
                for resource_id in existing_ids:
                    cache_key = cache_key_generator(resource_id)
                    await self.redis.delete(cache_key)

            # 触发批量删除事件
            if event_prefix:
                dispatch(f"{event_prefix}:batch_deleted", payload={
                    "deleted_ids": existing_ids,
                    "deleted_count": deleted_count,
                    "soft_delete": soft_delete
                })

            response = BatchDeleteResponse(
                deleted_count=deleted_count,
                failed_ids=failed_ids,
                total_requested=len(resource_ids)
            )

            self.logger.info(f"批量删除{resource_type}完成: 成功删除{deleted_count}个{resource_type}，失败{len(failed_ids)}个")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"批量删除{resource_type}失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.DELETE_FAILED,
                error_message=f"批量删除{resource_type}失败: {str(e)}"
            )