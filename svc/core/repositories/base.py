"""
基础仓储模式实现。
提供通用的数据访问方法，所有特定模型的仓储类都应该继承这个基类。
支持基本的CRUD操作，并可选地支持创建和更新模式的类型。
"""
from typing import (Any, Dict, Generic, List, Optional, Tuple, Type, TypeVar,
                    Union)

from sqlalchemy import Column, asc, delete, desc, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import Select

# 定义模型类型变量
ModelType = TypeVar("ModelType")
# 定义创建数据类型变量（可选）
CreateSchemaType = TypeVar("CreateSchemaType")
# 定义更新数据类型变量（可选）
UpdateSchemaType = TypeVar("UpdateSchemaType")

class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    通用仓储基类，提供基本的CRUD操作。
    所有模型特定的仓储类都应该继承这个基类。
    
    类型参数:
        ModelType: 数据库模型类型（必需）
        CreateSchemaType: 创建数据模式类型（可选）
        UpdateSchemaType: 更新数据模式类型（可选）
        
    用法示例:
        1. 基本用法（只使用模型类型）:
           ```python
           class UserRepository(BaseRepository[User]):
               pass
           ```
        
        2. 完整用法（包含创建和更新模式）:
           ```python
           class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
               pass
           ```
    """
    
    def __init__(self,db:AsyncSession, model: Type[ModelType]):
        """
        初始化仓储基类
        
        Args:
            model: SQLAlchemy模型类
        """
        self.model = model
        self.db = db
    
    def _apply_filters(self, query: Select, **filters) -> Select:
        """
        将过滤条件应用于查询对象
        
        Args:
            query: SQLAlchemy查询对象
            **filters: 过滤条件，格式为字段名=值
            
        Returns:
            Select: 应用了过滤条件的查询对象
        """
        conditions = []
        for field, value in filters.items():
            if hasattr(self.model, field):
                conditions.append(getattr(self.model, field) == value)
        
        if conditions:
            return query.where(*conditions)
        return query
    
    async def get_by_id(
        self, 
        id: Any,
        id_field: str = "id"
    ) -> Optional[ModelType]:
        """
        通过ID获取实体
        
        Args:
            db: 数据库会话
            id: 实体ID
            id_field: ID字段名，默认为'id'
            
        Returns:
            Optional[ModelType]: 实体对象，不存在则返回None
        """
        if not hasattr(self.model, id_field):
            # 如果指定的id_field在模型中不存在，可以抛出错误或返回None
            # 这里选择返回None，保持与原行为一致
            return None
        return await self.get_one(**{id_field: id})
    
    async def get_one(
        self, 
        **filters
    ) -> Optional[ModelType]:
        """
        根据条件获取单个实体
        
        Args:
            db: 数据库会话
            **filters: 过滤条件，格式为字段名=值
            
        Returns:
            Optional[ModelType]: 实体对象，不存在则返回None
        """
        if not filters:
            return None # 没有过滤条件，无法确定唯一实体
        
        query = select(self.model)
        query = self._apply_filters(query, **filters)
        
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_list(
        self, 
        *,
        skip: int = 0, 
        limit: int = 100,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        **filters
    ) -> List[ModelType]:
        """
        获取实体列表
        
        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数上限
            order_by: 排序字段，可以是字段名字符串或SQLAlchemy列对象
            order_direction: 排序方向，'asc'升序或'desc'降序
            **filters: 过滤条件，格式为字段名=值
            
        Returns:
            List[ModelType]: 实体列表
        """
        # 确保 skip 不为负数
        safe_skip = max(0, skip)
        
        query = self._build_query(order_by, order_direction, **filters)
        
        # 添加分页
        query = query.offset(safe_skip).limit(limit)
        
        # 执行查询
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def count(
        self, 
        **filters
    ) -> int:
        """
        计算符合条件的记录数量
        
        Args:
            db: 数据库会话
            **filters: 过滤条件，格式为字段名=值
            
        Returns:
            int: 记录数量
        """
        # 构建计数查询，只选择主键或第一个字段进行计数优化
        # 如果模型有 'id' 字段，优先使用它
        count_column = getattr(self.model, 'id', list(self.model.__table__.columns)[0])
        query = select(func.count(count_column)).select_from(self.model)
        
        # 应用过滤条件
        query = self._apply_filters(query, **filters)
        
        # 执行查询
        result = await self.db.execute(query)
        # scalar_one() 期望正好一个结果，如果可能没有匹配项，scalar() 更安全
        count = result.scalar()
        return count if count is not None else 0
    
    async def get_paginated(
        self, 
        *,
        page_num: int = 1,
        page_size: int = 10,
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        with_total: bool = True,
        **filters
    ) -> Tuple[List[ModelType], Optional[int]]:
        """
        获取分页的实体列表及总数
        Args:
            page: 页码，从1开始
            page_size: 每页大小
            order_by: 排序字段
            order_direction: 排序方向
            with_total: 是否查询总数
            **filters: 过滤条件
        Returns:
            (items, total) 元组，total为None表示未查总数
        """
        skip = (page_num - 1) * page_size
        query = self._build_query(order_by, order_direction, **filters)
        query = query.offset(skip).limit(page_size)
        result = await self.db.execute(query)
        items = result.scalars().all()
        total = None
        if with_total:
            total = await self.count(**filters)
        return items, total
    
    async def create(
        self, 
        data: Any
    ) -> ModelType:
        """
        创建新实体
        Args:
            db: 数据库会话
            data: 实体数据字典或Pydantic对象
        Returns:
            ModelType: 创建的实体对象
        """
        # 支持Pydantic对象和dict
        if hasattr(data, 'model_dump'):
            model_data = data.model_dump()
        else:
            model_data = data
        model_data = {k: v for k, v in model_data.items() if hasattr(self.model, k)}
        obj = self.model(**model_data)
        self.db.add(obj)
        await self.db.flush()
        await self.db.refresh(obj)
        return obj
    
    async def update(
        self, 
        db_obj: ModelType, 
        data: Dict[str, Any]
    ) -> ModelType:
        """
        更新实体
        
        Args:
            db: 数据库会话
            db_obj: 要更新的实体对象
            data: 更新数据字典
            
        Returns:
            ModelType: 更新后的实体对象
        """
        # 更新对象属性，只更新data中提供的且模型拥有的属性
        for field, value in data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        await self.db.flush()
        await self.db.refresh(db_obj)
        return db_obj
    
    async def update_by_id(
        self, 
         
        id: Any, 
        data: Dict[str, Any],
        id_field: str = "id"
    ) -> Optional[ModelType]:
        """
        通过ID更新实体
        
        Args:
            db: 数据库会话
            id: 实体ID
            data: 更新数据字典
            id_field: ID字段名，默认为'id'
            
        Returns:
            Optional[ModelType]: 更新后的实体对象，不存在则返回None
        """
        db_obj = await self.get_by_id(self.db, id, id_field=id_field)
        if not db_obj:
            return None
        
        return await self.update(self.db, db_obj, data)
    
    async def delete(
        self, 
        db_obj: ModelType
    ) -> None:
        """
        删除实体
        
        Args:
            db: 数据库会话
            db_obj: 要删除的实体对象
        """
        await self.db.delete(db_obj)
        await self.db.flush()
    
    async def delete_by_id(
        self, 
        id: Any,
        id_field: str = "id"
    ) -> bool:
        """
        通过ID删除实体
        
        Args:
            db: 数据库会话
            id: 实体ID
            id_field: ID字段名，默认为'id'
            
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        db_obj = await self.get_by_id(self.db, id, id_field=id_field)
        if not db_obj:
            return False
        
        await self.delete(self.db, db_obj)
        return True
    
    def _build_query(
        self, 
        order_by: Optional[Union[str, Column]] = None,
        order_direction: str = "asc",
        **filters
    ) -> Select:
        """
        构建查询对象（包含过滤和排序）
        
        Args:
            order_by: 排序字段，可以是字段名字符串或SQLAlchemy列对象
            order_direction: 排序方向，'asc'升序或'desc'降序
            **filters: 过滤条件，格式为字段名=值
            
        Returns:
            Select: SQLAlchemy查询对象
        """
        # 创建基础查询
        query = select(self.model)
        
        # 添加过滤条件
        query = self._apply_filters(query, **filters)
        
        # 添加排序
        if order_by:
            order_column: Optional[Column] = None
            # 如果order_by是字符串，转换为列对象
            if isinstance(order_by, str):
                if hasattr(self.model, order_by):
                    order_column = getattr(self.model, order_by)
                else:
                    # 如果模型没有该属性，尝试使用默认排序 'id'
                    # 如果连 'id' 都没有，则不排序
                    if hasattr(self.model, "id"):
                        order_column = getattr(self.model, "id")
            elif isinstance(order_by, Column) and order_by.class_ == self.model:
                 # 确保列对象属于当前模型
                 order_column = order_by
            
            # 应用排序方向
            if order_column is not None:
                if order_direction.lower() == "desc":
                    query = query.order_by(desc(order_column))
                else:
                    query = query.order_by(asc(order_column))
            # else: 如果找不到有效的排序列，则不应用排序
        
        return query

    async def get_existing_ids(self, resource_ids: List[int], id_field: str = "id") -> List[int]:
        """获取存在的资源ID列表

        Args:
            resource_ids: 资源ID列表
            id_field: ID字段名，默认为'id'

        Returns:
            List[int]: 存在的资源ID列表
        """
        if not resource_ids:
            return []

        if not hasattr(self.model, id_field):
            return []

        id_column = getattr(self.model, id_field)
        query = select(id_column).where(id_column.in_(resource_ids))
        result = await self.db.execute(query)
        return result.scalars().all()

    async def batch_update(
        self,
        resource_ids: List[int],
        update_data: Dict[str, Any],
        id_field: str = "id",
        auto_update_timestamp: bool = True
    ) -> int:
        """批量更新资源

        Args:
            resource_ids: 资源ID列表
            update_data: 更新数据字典
            id_field: ID字段名，默认为'id'
            auto_update_timestamp: 是否自动更新时间戳

        Returns:
            int: 更新的记录数
        """
        if not resource_ids or not update_data:
            return 0

        if not hasattr(self.model, id_field):
            return 0

        # 过滤掉None值和空值
        filtered_data = {k: v for k, v in update_data.items() if v is not None}
        if not filtered_data:
            return 0

        # 自动添加更新时间戳
        if auto_update_timestamp and hasattr(self.model, 'updated_at'):
            from svc.core.utils.datetime_utils import \
                get_utc_now_without_tzinfo
            filtered_data['updated_at'] = get_utc_now_without_tzinfo()

        # 执行批量更新
        id_column = getattr(self.model, id_field)
        stmt = update(self.model).where(
            id_column.in_(resource_ids)
        ).values(**filtered_data)

        result = await self.db.execute(stmt)
        await self.db.flush()

        return result.rowcount

    async def batch_delete(
        self,
        resource_ids: List[int],
        id_field: str = "id",
        soft_delete: bool = True
    ) -> int:
        """批量删除资源

        Args:
            resource_ids: 资源ID列表
            id_field: ID字段名，默认为'id'
            soft_delete: 是否软删除

        Returns:
            int: 删除的记录数
        """
        if not resource_ids:
            return 0

        if not hasattr(self.model, id_field):
            return 0

        id_column = getattr(self.model, id_field)

        if soft_delete and hasattr(self.model, 'deleted_at'):
            # 软删除：设置deleted_at字段
            from svc.core.utils.datetime_utils import \
                get_utc_now_without_tzinfo
            update_data = {'deleted_at': get_utc_now_without_tzinfo()}

            # 如果有is_active字段，也设置为False
            if hasattr(self.model, 'is_active'):
                update_data['is_active'] = False

            stmt = update(self.model).where(
                id_column.in_(resource_ids)
            ).values(**update_data)
        else:
            # 硬删除：直接删除记录
            stmt = delete(self.model).where(id_column.in_(resource_ids))

        result = await self.db.execute(stmt)
        await self.db.flush()

        return result.rowcount