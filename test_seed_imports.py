#!/usr/bin/env python3
"""
测试种子数据脚本的导入问题
"""
import sys
import traceback


def test_imports():
    """测试各个模块的导入"""
    try:
        print("测试基础导入...")

        # 直接导入避免循环导入
        import sys
        sys.path.insert(0, '/Users/<USER>/Documents/aitools')

        from svc.apps.products.schemas.specs import SpecWithOptions
        print("✅ SpecWithOptions 导入成功")

        from svc.apps.products.repositories.specs import (
            ProductSpecCombinationRepository, SpecOptionRepository,
            SpecRepository)
        print("✅ 仓库类导入成功")

        # 延迟导入服务类
        from svc.apps.products.services.specs import \
            ProductSpecCombinationService
        print("✅ ProductSpecCombinationService 导入成功")

        from svc.apps.albums.services.album import AlbumService
        print("✅ AlbumService 导入成功")
        
        print("\n测试Schema创建...")
        spec = SpecWithOptions(
            name="测试规格",
            description="测试描述",
            options=["选项1", "选项2"],
            sort_order=1
        )
        print(f"✅ SpecWithOptions 创建成功: {spec}")
        
        print("\n所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        print("\n测试数据库连接...")
        from svc.core.database.session import get_session
        print("✅ 数据库会话导入成功")
        return True
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试种子数据脚本的依赖...")
    
    success = True
    success &= test_imports()
    success &= test_database_connection()
    
    if success:
        print("\n🎉 所有测试通过！可以运行种子数据脚本。")
        sys.exit(0)
    else:
        print("\n💥 测试失败！请检查错误信息。")
        sys.exit(1)
