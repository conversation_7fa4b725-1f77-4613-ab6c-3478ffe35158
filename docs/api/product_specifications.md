# 产品规格管理API文档

## 概述

产品规格管理API提供了一站式的产品规格配置和管理功能。重构后的API设计更加简洁易用，支持批量操作和高级查询功能。

## 核心概念

- **规格(Spec)**: 产品的属性维度，如"颜色"、"尺寸"、"存储容量"
- **规格值(SpecOption)**: 规格的具体选项，如"红色"、"XL"、"128GB"
- **规格组合(Combination)**: 不同规格值的组合，形成可售的SKU

## API端点

### 1. 一站式设置产品规格

**POST** `/products/{product_id}/specifications`

一次性设置产品的所有规格，自动生成所有可能的组合。

#### 请求示例

```json
{
  "specs": [
    {
      "name": "颜色",
      "description": "产品颜色选项",
      "sortOrder": 1,
      "options": ["红色", "蓝色", "黑色"]
    },
    {
      "name": "尺寸",
      "description": "产品尺寸选项", 
      "sortOrder": 2,
      "options": ["S", "M", "L", "XL"]
    }
  ],
  "autoGenerateCombinations": true,
  "defaultStockQuantity": 0
}
```

#### 响应示例

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "productId": 123,
      "sku": "PROD123-RED-S",
      "price": null,
      "stockQuantity": 0,
      "isActive": true,
      "specOptionIds": [1, 4],
      "albumId": null
    }
    // ... 更多组合
  ]
}
```

### 2. 获取产品完整规格信息

**GET** `/products/{product_id}/specifications`

获取产品的完整规格信息，包括规格定义、选项和所有组合。

#### 查询参数

- `includeInactive`: 是否包含已停用的组合 (默认: false)

#### 响应示例

```json
{
  "success": true,
  "data": {
    "productId": 123,
    "specs": [
      {
        "id": 1,
        "name": "颜色",
        "description": "产品颜色选项",
        "sortOrder": 1,
        "options": [
          {
            "id": 1,
            "value": "红色",
            "colorCode": "#FF0000",
            "imageUrl": null,
            "sortOrder": 1
          }
        ]
      }
    ],
    "combinations": [
      {
        "id": 1,
        "sku": "PROD123-RED-S",
        "price": 99.99,
        "stockQuantity": 10,
        "specOptionIds": [1, 4]
      }
    ],
    "stats": {
      "totalCombinations": 12,
      "activeCombinations": 10,
      "pricedCombinations": 8,
      "totalStock": 120,
      "avgPrice": 89.99
    }
  }
}
```

### 3. 批量更新组合价格

**PUT** `/products/{product_id}/combinations/pricing`

批量更新规格组合的价格信息。

#### 请求示例

```json
{
  "combinationIds": [1, 2, 3],
  "pricingData": {
    "price": 99.99,
    "costPrice": 60.00,
    "marketPrice": 129.99
  }
}
```

### 4. 复制规格配置

**POST** `/products/{target_product_id}/specifications/copy/{source_product_id}`

从源产品复制规格配置到目标产品。

#### 请求示例

```json
{
  "copyPricing": true,
  "copyStock": false,
  "copyImages": true
}
```

### 5. 根据规格值查找组合

**GET** `/products/{product_id}/combinations/search`

根据指定的规格值查找匹配的组合。

#### 查询参数

- `specOptionIds`: 规格值ID列表 (必需)

#### 示例

```
GET /products/123/combinations/search?specOptionIds=1&specOptionIds=4
```

### 6. 根据价格范围查找组合

**GET** `/products/{product_id}/combinations/price-range`

根据价格范围查找符合条件的组合。

#### 查询参数

- `minPrice`: 最低价格(元)
- `maxPrice`: 最高价格(元)

#### 示例

```
GET /products/123/combinations/price-range?minPrice=50&maxPrice=150
```

### 7. 快速设置产品规格

**POST** `/products/{product_id}/specifications/quick-setup`

简化版的规格设置接口，适用于简单场景。

#### 请求示例

```json
[
  {
    "name": "颜色",
    "options": ["红色", "蓝色"]
  },
  {
    "name": "尺寸", 
    "options": ["S", "M", "L"]
  }
]
```

### 8. 清空产品规格

**DELETE** `/products/{product_id}/specifications`

删除产品的所有规格组合。

### 9. 获取规格统计信息

**GET** `/products/{product_id}/specifications/stats`

获取产品规格的统计信息。

## 错误处理

所有API都使用统一的错误响应格式：

```json
{
  "success": false,
  "errorCode": "VALIDATION_ERROR",
  "errorMessage": "规格名称不能为空",
  "timestamp": "2025-01-26T10:00:00Z"
}
```

### 常见错误码

- `NOT_FOUND`: 资源不存在
- `VALIDATION_ERROR`: 输入验证失败
- `OPERATION_FAILED`: 操作执行失败
- `PERMISSION_DENIED`: 权限不足
- `DATABASE_ERROR`: 数据库操作失败

## 权限要求

- `product:update`: 创建和更新规格
- `product:delete`: 删除规格
- `product:read`: 查看规格信息

## 最佳实践

### 1. 规格设计建议

- 规格名称应该简洁明了
- 规格值应该有明确的排序
- 避免创建过多的规格组合

### 2. 性能优化

- 使用批量操作接口处理大量数据
- 合理使用分页参数
- 避免频繁的单个操作

### 3. 数据一致性

- 在设置规格前确保产品存在
- 使用事务确保数据一致性
- 及时处理失效的组合

## 示例场景

### 场景1: 新产品规格设置

```javascript
// 1. 设置产品规格
const response = await fetch('/products/123/specifications', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    specs: [
      { name: '颜色', options: ['红色', '蓝色'] },
      { name: '尺寸', options: ['S', 'M', 'L'] }
    ]
  })
});

// 2. 更新价格
await fetch('/products/123/combinations/pricing', {
  method: 'PUT',
  body: JSON.stringify({
    combinationIds: [1, 2, 3],
    pricingData: { price: 99.99 }
  })
});
```

### 场景2: 规格查询和筛选

```javascript
// 1. 获取完整规格信息
const specs = await fetch('/products/123/specifications');

// 2. 根据规格值查找组合
const combinations = await fetch(
  '/products/123/combinations/search?specOptionIds=1&specOptionIds=4'
);

// 3. 根据价格范围筛选
const pricedCombinations = await fetch(
  '/products/123/combinations/price-range?minPrice=50&maxPrice=150'
);
```

## 版本历史

- **v2.0**: 重构API，简化接口设计，提供一站式服务
- **v1.0**: 初始版本，分离的规格管理接口
