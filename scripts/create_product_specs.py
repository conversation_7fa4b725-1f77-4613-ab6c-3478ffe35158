#!/usr/bin/env python3
"""
专门用于创建产品规格的脚本
解决循环导入问题，专注于产品规格功能演示

使用方法：
python scripts/create_product_specs.py --product-id 1
python scripts/create_product_specs.py --all-products
python scripts/create_product_specs.py --demo
"""
import argparse
import asyncio
import logging
import random
import time
from typing import List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('product_specs')


async def get_database_session():
    """获取数据库会话"""
    from svc.core.database.session import get_session
    return get_session()


async def get_spec_service(db):
    """获取规格服务实例"""
    # 延迟导入避免循环依赖
    from svc.apps.albums.services.album import AlbumService
    from svc.apps.products.repositories.specs import (
        ProductSpecCombinationRepository, SpecOptionRepository, SpecRepository
    )
    from svc.apps.products.services.specs import ProductSpecCombinationService
    
    # 初始化仓库
    spec_repo = SpecRepository(db)
    option_repo = SpecOptionRepository(db)
    combination_repo = ProductSpecCombinationRepository(db)
    album_service = AlbumService(db)
    
    # 创建服务实例
    return ProductSpecCombinationService(
        repo=combination_repo,
        album_service=album_service,
        basic_spec_repo=spec_repo,
        spec_option_repo=option_repo
    )


async def get_products(db, product_id: Optional[int] = None) -> List:
    """获取产品列表"""
    from svc.apps.products.repositories.product import ProductRepository
    
    repo = ProductRepository(db)
    
    if product_id:
        product = await repo.get(product_id)
        return [product] if product else []
    else:
        products, _ = await repo.get_products()
        return products


def get_helmet_specs():
    """获取头盔规格配置"""
    from svc.apps.products.schemas.specs import SpecWithOptions
    
    return [
        SpecWithOptions(
            name="尺寸",
            description="头盔尺寸规格",
            options=["S", "M", "L"],
            sort_order=1
        ),
        SpecWithOptions(
            name="颜色",
            description="头盔颜色规格",
            options=["玄青", "朱砂", "月白", "石青", "黛绿"],
            sort_order=2
        ),
        SpecWithOptions(
            name="材质",
            description="头盔材质规格",
            options=["碳纤维", "ABS"],
            sort_order=3
        )
    ]


async def create_product_specifications(product_id: int) -> bool:
    """为指定产品创建规格"""
    try:
        async with await get_database_session() as db:
            # 获取服务和产品
            spec_service = await get_spec_service(db)
            products = await get_products(db, product_id)
            
            if not products:
                logger.error(f"产品 {product_id} 不存在")
                return False
            
            product = products[0]
            logger.info(f"为产品 {product.name} (ID: {product.id}) 创建规格...")
            
            # 检查是否已有规格
            existing_result = await spec_service.get_product_specifications(product.id)
            if existing_result.is_success and existing_result.data.combinations:
                logger.info(f"产品 {product.name} 已有 {len(existing_result.data.combinations)} 个规格组合")
                return True
            
            # 创建规格
            helmet_specs = get_helmet_specs()
            result = await spec_service.setup_product_specifications(
                product_id=product.id,
                specs_data=helmet_specs,
                user_id=1
            )
            
            if result.is_success:
                combinations = result.data
                logger.info(f"✅ 成功为产品 {product.name} 创建了 {len(combinations)} 个规格组合")
                
                # 设置随机价格
                await set_random_pricing(spec_service, combinations, product)
                
                await db.commit()
                return True
            else:
                logger.error(f"❌ 创建规格失败: {result.message}")
                return False
                
    except Exception as e:
        logger.error(f"❌ 创建产品规格时发生异常: {str(e)}", exc_info=True)
        return False


async def set_random_pricing(spec_service, combinations, product):
    """设置随机价格"""
    logger.info("设置随机价格和库存...")
    
    for combo in combinations:
        price_adjustment = random.randint(-500, 1000)  # 价格浮动
        stock_quantity = random.randint(5, 50)  # 库存
        
        pricing_data = {
            "price": (product.price + price_adjustment) / 100,  # 转换为元
            "cost_price": (product.price + price_adjustment) * 0.6 / 100,
            "market_price": (product.price + price_adjustment) * 1.2 / 100,
            "stock_quantity": stock_quantity
        }
        
        await spec_service.update_combination_pricing(
            combination_ids=[combo.id],
            pricing_data=pricing_data,
            user_id=1
        )


async def create_all_products_specifications() -> bool:
    """为所有产品创建规格"""
    try:
        async with await get_database_session() as db:
            spec_service = await get_spec_service(db)
            products = await get_products(db)
            
            if not products:
                logger.warning("没有找到任何产品")
                return False
            
            logger.info(f"为 {len(products)} 个产品创建规格...")
            success_count = 0
            
            for product in products:
                try:
                    # 检查是否已有规格
                    existing_result = await spec_service.get_product_specifications(product.id)
                    if existing_result.is_success and existing_result.data.combinations:
                        logger.info(f"产品 {product.name} 已有规格，跳过")
                        success_count += 1
                        continue
                    
                    # 创建规格
                    helmet_specs = get_helmet_specs()
                    result = await spec_service.setup_product_specifications(
                        product_id=product.id,
                        specs_data=helmet_specs,
                        user_id=1
                    )
                    
                    if result.is_success:
                        logger.info(f"✅ 产品 {product.name}: {len(result.data)} 个组合")
                        await set_random_pricing(spec_service, result.data, product)
                        success_count += 1
                    else:
                        logger.error(f"❌ 产品 {product.name} 失败: {result.message}")
                        
                except Exception as e:
                    logger.error(f"❌ 产品 {product.name} 异常: {str(e)}")
                    continue
            
            await db.commit()
            logger.info(f"完成! 成功处理 {success_count}/{len(products)} 个产品")
            return success_count > 0
            
    except Exception as e:
        logger.error(f"❌ 批量创建规格时发生异常: {str(e)}", exc_info=True)
        return False


async def demo_specification_features():
    """演示规格管理功能"""
    try:
        async with await get_database_session() as db:
            spec_service = await get_spec_service(db)
            products = await get_products(db)
            
            if not products:
                logger.warning("没有产品可用于演示")
                return
            
            demo_product = products[0]
            logger.info(f"🎯 使用产品 {demo_product.name} 进行功能演示")
            
            # 1. 演示获取规格信息
            logger.info("📋 获取完整规格信息...")
            result = await spec_service.get_product_specifications(demo_product.id)
            if result.is_success:
                data = result.data
                logger.info(f"  - 规格数量: {len(data.specs)}")
                logger.info(f"  - 组合数量: {len(data.combinations)}")
                logger.info(f"  - 统计信息: {data.stats}")
            
            # 2. 演示缓存效果
            logger.info("⚡ 测试缓存效果...")
            times = []
            for i in range(3):
                start_time = time.time()
                await spec_service.get_product_specifications(demo_product.id)
                end_time = time.time()
                times.append((end_time - start_time) * 1000)
                logger.info(f"  第{i+1}次查询: {times[-1]:.2f}ms")
            
            # 3. 演示批量价格更新
            if result.is_success and result.data.combinations:
                logger.info("💰 演示批量价格更新...")
                combinations = result.data.combinations[:3]  # 取前3个
                combination_ids = [combo.id for combo in combinations]
                
                new_pricing = {
                    "price": 299.99,
                    "cost_price": 180.00,
                    "market_price": 399.99
                }
                
                update_result = await spec_service.update_combination_pricing(
                    combination_ids=combination_ids,
                    pricing_data=new_pricing,
                    user_id=1
                )
                
                if update_result.is_success:
                    logger.info(f"  ✅ 成功更新 {len(combination_ids)} 个组合的价格")
                else:
                    logger.error(f"  ❌ 价格更新失败: {update_result.message}")
            
            await db.commit()
            logger.info("🎉 演示完成!")
            
    except Exception as e:
        logger.error(f"❌ 演示过程中发生异常: {str(e)}", exc_info=True)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="产品规格创建脚本")
    parser.add_argument("--product-id", type=int, help="为指定产品创建规格")
    parser.add_argument("--all-products", action="store_true", help="为所有产品创建规格")
    parser.add_argument("--demo", action="store_true", help="运行功能演示")
    
    args = parser.parse_args()
    
    if args.product_id:
        success = await create_product_specifications(args.product_id)
        return 0 if success else 1
    elif args.all_products:
        success = await create_all_products_specifications()
        return 0 if success else 1
    elif args.demo:
        await demo_specification_features()
        return 0
    else:
        parser.print_help()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
