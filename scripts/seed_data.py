#!/usr/bin/env python3
"""
重构后的种子数据脚本
解决循环导入问题，提高可维护性和性能

特点：
1. 模块化设计，按功能拆分
2. 延迟导入，避免循环依赖
3. 异步并发，提高执行效率
4. 错误隔离，单个模块失败不影响其他模块

使用方法：
python scripts/seed_data.py --reset              # 重置数据库并运行完整演示
python scripts/seed_data.py --reset --no-demos   # 重置数据库但跳过演示
python scripts/seed_data.py --recreate           # 重新创建数据库并运行完整演示
"""
import argparse
import asyncio
import logging
import os
import random
import subprocess
import time
from typing import Dict, List, Optional

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('seed_data')


class DatabaseManager:
    """数据库管理器"""

    @staticmethod
    def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
        """运行命令并打印输出"""
        logger.info(f"执行命令: {command}")
        return subprocess.run(command, shell=True, check=check)

    @staticmethod
    def reset_database(recreate: bool = False) -> None:
        """重置数据库"""
        if recreate:
            # 获取数据库连接信息
            db_name = os.getenv("POSTGRES_DB", "fastapi_nano")
            db_user = os.getenv("POSTGRES_USER", "postgres")
            db_password = os.getenv("POSTGRES_PASSWORD", "postgres")
            db_server = os.getenv("POSTGRES_SERVER", "localhost")

            # 删除数据库
            logger.info(f"删除数据库 {db_name}...")
            DatabaseManager.run_command(
                f"PGPASSWORD={db_password} dropdb -h {db_server} -U {db_user} {db_name}",
                check=False
            )

            # 创建数据库
            logger.info(f"创建数据库 {db_name}...")
            DatabaseManager.run_command(
                f"PGPASSWORD={db_password} createdb -h {db_server} -U {db_user} {db_name}"
            )
        else:
            # 回滚所有迁移
            logger.info("回滚所有迁移...")
            DatabaseManager.run_command("alembic downgrade base")

        # 应用所有迁移
        logger.info("应用所有迁移...")
        DatabaseManager.run_command("alembic upgrade head")
        logger.info("数据库重置完成！")


class SeedDataModule:
    """种子数据模块基类"""

    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f'seed_data.{name}')

    async def create_data(self, db, **kwargs) -> Dict:
        """创建种子数据 - 子类需要实现"""
        raise NotImplementedError

    async def run(self, db, **kwargs) -> Dict:
        """运行种子数据创建"""
        try:
            self.logger.info(f"开始创建 {self.name} 种子数据...")
            result = await self.create_data(db, **kwargs)
            self.logger.info(f"✅ {self.name} 种子数据创建完成")
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"❌ {self.name} 种子数据创建失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}


class AuthModule(SeedDataModule):
    """认证模块种子数据"""

    def __init__(self):
        super().__init__("auth")

    async def create_data(self, db, **kwargs) -> Dict:
        """创建认证相关数据"""
        # 延迟导入避免循环依赖
        from svc.apps.auth.models import Permission, Role, User
        from svc.core.database.session_utils import get_session_for_script

        # 创建基础数据
        permissions = await self._create_permissions(db)
        roles = await self._create_roles(db, permissions)
        users = await self._create_users(db, roles)

        await db.commit()

        return {
            "permissions": len(permissions),
            "roles": len(roles),
            "users": len(users)
        }

    async def _create_permissions(self, db) -> List:
        """创建权限数据"""
        from sqlalchemy import select

        from svc.apps.auth.models import Permission

        # 检查是否已有权限
        result = await db.execute(select(Permission))
        existing_permissions = result.scalars().all()
        if existing_permissions:
            self.logger.info("权限数据已存在，跳过创建")
            return existing_permissions

        # 定义权限列表
        permission_data = [
            {"name": "user:read", "description": "查看用户"},
            {"name": "user:create", "description": "创建用户"},
            {"name": "user:update", "description": "更新用户"},
            {"name": "user:delete", "description": "删除用户"},
            {"name": "product:read", "description": "查看产品"},
            {"name": "product:create", "description": "创建产品"},
            {"name": "product:update", "description": "更新产品"},
            {"name": "product:delete", "description": "删除产品"},
            {"name": "admin:all", "description": "管理员权限"},
        ]

        permissions = []
        for perm_data in permission_data:
            permission = Permission(**perm_data)
            db.add(permission)
            permissions.append(permission)
            self.logger.info(f"创建权限: {perm_data['name']}")

        await db.flush()  # 获取ID
        return permissions

    async def _create_roles(self, db, permissions) -> List:
        """创建角色数据"""
        from sqlalchemy import select

        from svc.apps.auth.models import Role

        # 检查是否已有角色
        result = await db.execute(select(Role))
        existing_roles = result.scalars().all()
        if existing_roles:
            self.logger.info("角色数据已存在，跳过创建")
            return existing_roles

        # 创建角色
        roles_data = [
            {"name": "admin", "description": "系统管理员"},
            {"name": "user", "description": "普通用户"},
            {"name": "guest", "description": "访客用户"},
        ]

        roles = []
        for role_data in roles_data:
            role = Role(**role_data)
            db.add(role)
            roles.append(role)
            self.logger.info(f"创建角色: {role_data['name']}")

        await db.flush()
        return roles

    async def _create_users(self, db, roles) -> List:
        """创建用户数据"""
        from sqlalchemy import select

        from svc.apps.auth.models import User

        # 检查是否已有用户
        result = await db.execute(select(User))
        existing_users = result.scalars().all()
        if existing_users:
            self.logger.info("用户数据已存在，跳过创建")
            return existing_users

        # 创建管理员用户
        admin_role = next((r for r in roles if r.name == "admin"), None)
        if admin_role:
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # admin123
                is_active=True
            )
            # 关联角色
            admin_user.roles.append(admin_role)
            db.add(admin_user)
            self.logger.info("创建管理员用户: admin")

            await db.flush()
            return [admin_user]

        return []


class ProductModule(SeedDataModule):
    """产品模块种子数据"""

    def __init__(self):
        super().__init__("product")

    async def create_data(self, db, **kwargs) -> Dict:
        """创建产品相关数据"""
        # 创建分类
        categories = await self._create_categories(db)

        # 创建产品
        products = await self._create_products(db, categories)

        # 创建产品规格
        if kwargs.get('create_specs', True):
            await self._create_product_specs(db, products)

        await db.commit()

        return {
            "categories": len(categories),
            "products": len(products)
        }

    async def _create_categories(self, db) -> List:
        """创建分类数据"""
        from sqlalchemy import select

        from svc.apps.products.models.category import Category

        # 检查是否已有分类
        result = await db.execute(select(Category))
        existing_categories = result.scalars().all()
        if existing_categories:
            self.logger.info("分类数据已存在，跳过创建")
            return existing_categories

        helmet_types = ["安全帽", "骑行盔", "摩托盔", "滑雪盔", "攀岩盔", "马术盔"]
        categories = []

        for idx, name in enumerate(helmet_types):
            category = Category(
                name=name,
                description=f"{name}类头盔产品",
                slug=f"helmet-{idx+1}",
                is_active=True
            )
            db.add(category)
            categories.append(category)
            self.logger.info(f"创建分类: {name}")

        await db.flush()
        return categories

    async def _create_products(self, db, categories) -> List:
        """创建产品数据"""
        from sqlalchemy import select

        from svc.apps.products.models.product import Product

        # 检查是否已有产品
        result = await db.execute(select(Product))
        existing_products = result.scalars().all()
        if existing_products:
            self.logger.info("产品数据已存在，跳过创建")
            return existing_products

        # 产品名称列表
        product_names = [
            "专业安全头盔", "时尚骑行头盔", "越野摩托头盔",
            "滑雪防护头盔", "攀岩安全头盔", "马术防护头盔"
        ]

        products = []
        for idx, name in enumerate(product_names):
            category = categories[idx % len(categories)]
            product = Product(
                name=name,
                description=f"高品质{name}，提供全方位安全保护",
                price=random.randint(15000, 50000),  # 150-500元（以分为单位）
                sku=f"HELMET-{idx+1:03d}",
                category_id=category.id,
                is_active=True,
                stock_quantity=random.randint(50, 200)
            )
            db.add(product)
            products.append(product)
            self.logger.info(f"创建产品: {name}")

        await db.flush()
        return products

    async def _create_product_specs(self, db, products) -> None:
        """创建产品规格数据"""
        # 延迟导入避免循环依赖
        from svc.apps.albums.services.album import AlbumService
        from svc.apps.products.repositories.specs import (
            ProductSpecCombinationRepository, SpecOptionRepository,
            SpecRepository)
        from svc.apps.products.schemas.specs import SpecWithOptions
        from svc.apps.products.services.specs import \
            ProductSpecCombinationService

        # 初始化服务
        spec_repo = SpecRepository(db)
        option_repo = SpecOptionRepository(db)
        combination_repo = ProductSpecCombinationRepository(db)
        album_service = AlbumService(db)

        spec_service = ProductSpecCombinationService(
            repo=combination_repo,
            album_service=album_service,
            basic_spec_repo=spec_repo,
            spec_option_repo=option_repo
        )

        # 定义规格配置
        helmet_specs = [
            SpecWithOptions(
                name="尺寸",
                description="头盔尺寸规格",
                options=["S", "M", "L"],
                sort_order=1
            ),
            SpecWithOptions(
                name="颜色",
                description="头盔颜色规格",
                options=["玄青", "朱砂", "月白", "石青", "黛绿"],
                sort_order=2
            ),
            SpecWithOptions(
                name="材质",
                description="头盔材质规格",
                options=["碳纤维", "ABS"],
                sort_order=3
            )
        ]

        # 为每个产品设置规格
        for product in products:
            try:
                # 检查是否已有规格
                existing_result = await spec_service.get_product_specifications(product.id)
                if existing_result.is_success and existing_result.data.combinations:
                    self.logger.info(f"产品 {product.name} 已有规格，跳过")
                    continue

                # 创建规格
                result = await spec_service.setup_product_specifications(
                    product_id=product.id,
                    specs_data=helmet_specs,
                    user_id=1
                )

                if result.is_success:
                    combinations = result.data
                    self.logger.info(f"为产品 {product.name} 创建了 {len(combinations)} 个规格组合")

                    # 设置随机价格
                    await self._set_random_pricing(spec_service, combinations, product)
                else:
                    self.logger.error(f"产品 {product.name} 规格创建失败: {result.message}")

            except Exception as e:
                self.logger.error(f"产品 {product.name} 规格创建异常: {str(e)}")
                continue

    async def _set_random_pricing(self, spec_service, combinations, product):
        """设置随机价格"""
        for combo in combinations:
            price_adjustment = random.randint(-500, 1000)  # 价格浮动
            stock_quantity = random.randint(5, 50)  # 库存

            pricing_data = {
                "price": (product.price + price_adjustment) / 100,  # 转换为元
                "cost_price": (product.price + price_adjustment) * 0.6 / 100,
                "market_price": (product.price + price_adjustment) * 1.2 / 100,
                "stock_quantity": stock_quantity
            }

            await spec_service.update_combination_pricing(
                combination_ids=[combo.id],
                pricing_data=pricing_data,
                user_id=1
            )


class SeedDataManager:
    """种子数据管理器"""

    def __init__(self):
        self.modules = {
            'auth': AuthModule(),
            'product': ProductModule(),
        }

    async def run_all(self, run_demos: bool = True) -> Dict:
        """运行所有种子数据创建"""
        # 延迟导入数据库会话
        from svc.core.database.session import get_session

        results = {}

        async with get_session() as db:
            logger.info("开始创建种子数据...")

            # 按顺序执行各模块
            for module_name, module in self.modules.items():
                result = await module.run(db, create_specs=(module_name == 'product'))
                results[module_name] = result

                if not result['success']:
                    logger.warning(f"模块 {module_name} 创建失败，继续执行其他模块")

            # 运行演示功能
            if run_demos:
                await self._run_demos(db, results)

            logger.info("种子数据创建完成！")

        return results

    async def _run_demos(self, db, results) -> None:
        """运行演示功能"""
        if 'product' in results and results['product']['success']:
            products_data = results['product']['data']
            if products_data.get('products', 0) > 0:
                await self._demo_batch_pricing(db)
                await self._demo_specification_features(db)

    async def _demo_batch_pricing(self, db) -> None:
        """演示批量价格更新"""
        # 延迟导入
        from sqlalchemy import select

        from svc.apps.albums.services.album import AlbumService
        from svc.apps.products.models.product import Product
        from svc.apps.products.repositories.specs import (
            ProductSpecCombinationRepository, SpecOptionRepository,
            SpecRepository)
        from svc.apps.products.services.specs import \
            ProductSpecCombinationService

        logger.info("🎯 演示批量价格更新功能...")

        # 获取第一个产品
        result = await db.execute(select(Product).limit(1))
        product = result.scalar_one_or_none()

        if not product:
            logger.info("没有产品可用于演示")
            return

        # 初始化服务
        spec_repo = SpecRepository(db)
        option_repo = SpecOptionRepository(db)
        combination_repo = ProductSpecCombinationRepository(db)
        album_service = AlbumService(db)

        spec_service = ProductSpecCombinationService(
            repo=combination_repo,
            album_service=album_service,
            basic_spec_repo=spec_repo,
            spec_option_repo=option_repo
        )

        # 获取产品规格组合
        spec_result = await spec_service.get_product_specifications(product.id)
        if spec_result.is_success and spec_result.data.combinations:
            combinations = spec_result.data.combinations[:3]  # 取前3个
            combination_ids = [combo.id for combo in combinations]

            new_pricing = {
                "price": 299.99,
                "cost_price": 180.00,
                "market_price": 399.99
            }

            update_result = await spec_service.update_combination_pricing(
                combination_ids=combination_ids,
                pricing_data=new_pricing,
                user_id=1
            )

            if update_result.is_success:
                logger.info(f"✅ 成功批量更新 {len(combination_ids)} 个组合的价格")
            else:
                logger.error(f"❌ 批量价格更新失败: {update_result.message}")

        await db.commit()

    async def _demo_specification_features(self, db) -> None:
        """演示规格管理功能"""
        # 延迟导入
        from sqlalchemy import select

        from svc.apps.albums.services.album import AlbumService
        from svc.apps.products.models.product import Product
        from svc.apps.products.repositories.specs import (
            ProductSpecCombinationRepository, SpecOptionRepository,
            SpecRepository)
        from svc.apps.products.services.specs import \
            ProductSpecCombinationService

        logger.info("📊 演示规格管理功能...")

        # 获取第一个产品
        result = await db.execute(select(Product).limit(1))
        product = result.scalar_one_or_none()

        if not product:
            logger.info("没有产品可用于演示")
            return

        # 初始化服务
        spec_repo = SpecRepository(db)
        option_repo = SpecOptionRepository(db)
        combination_repo = ProductSpecCombinationRepository(db)
        album_service = AlbumService(db)

        spec_service = ProductSpecCombinationService(
            repo=combination_repo,
            album_service=album_service,
            basic_spec_repo=spec_repo,
            spec_option_repo=option_repo
        )

        # 演示获取规格信息
        logger.info(f"获取产品 {product.name} 的规格信息...")
        result = await spec_service.get_product_specifications(product.id)
        if result.is_success:
            data = result.data
            logger.info(f"  - 规格数量: {len(data.specs)}")
            logger.info(f"  - 组合数量: {len(data.combinations)}")
            logger.info(f"  - 统计信息: {data.stats}")

        # 演示缓存效果
        logger.info("⚡ 测试缓存效果...")
        times = []
        for i in range(3):
            start_time = time.time()
            await spec_service.get_product_specifications(product.id)
            end_time = time.time()
            times.append((end_time - start_time) * 1000)
            logger.info(f"  第{i+1}次查询: {times[-1]:.2f}ms")

        logger.info("🎉 演示完成!")


async def main(recreate: bool = False, reset: bool = False, run_demos: bool = True):
    """主函数"""
    try:
        # 重置数据库
        if recreate or reset:
            DatabaseManager.reset_database(recreate=recreate)

        # 创建种子数据
        manager = SeedDataManager()
        results = await manager.run_all(run_demos=run_demos)

        # 输出结果摘要
        success_count = sum(1 for r in results.values() if r['success'])
        total_count = len(results)

        logger.info(f"种子数据创建完成: {success_count}/{total_count} 个模块成功")

        # 输出详细结果
        for module_name, result in results.items():
            if result['success']:
                data = result['data']
                logger.info(f"✅ {module_name}: {data}")
            else:
                logger.error(f"❌ {module_name}: {result['error']}")

        return results

    except Exception as e:
        logger.error(f"种子数据创建失败: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="重构后的种子数据脚本")
    parser.add_argument("--recreate", action="store_true", help="重新创建数据库")
    parser.add_argument("--reset", action="store_true", help="重置数据库")
    parser.add_argument("--no-demos", action="store_true", help="跳过演示功能")

    args = parser.parse_args()

    asyncio.run(main(
        recreate=args.recreate,
        reset=args.reset,
        run_demos=not args.no_demos
    ))