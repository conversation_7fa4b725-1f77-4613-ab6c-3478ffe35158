"""
数据库种子数据脚本。
用于初始化数据库的基础数据。
"""
import argparse
import asyncio
import json
import logging
import os
import random
import subprocess
import sys
import unittest.mock
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import List, Optional

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 模拟事件总线，避免"no running event loop"错误
with unittest.mock.patch('asyncio.create_task'):
    from sqlalchemy import insert
    from sqlalchemy.ext.asyncio import AsyncSession
    from sqlalchemy.future import select

    from svc.apps.albums.repositories.album import AlbumRepository
    from svc.apps.albums.repositories.album_image import AlbumImageRepository
    from svc.apps.albums.schemas.album import AlbumCreate
    from svc.apps.albums.schemas.album_image import AlbumImageCreate
    from svc.apps.auth.models import Permission, Role, User, user_role
    from svc.apps.auth.models.wechat_user import WechatUser
    from svc.apps.auth.repositories import RoleRepository, UserRepository
    from svc.apps.billing.models.subscription_plan import SubscriptionPlan
    from svc.apps.billing.repositories import SubscriptionPlanRepository
    from svc.apps.marketing.models.campaign import (AntiAbuseStrategy,
                                                    Campaign, CampaignStatus)
    from svc.apps.marketing.models.invitation import Invitation
    from svc.apps.marketing.models.reward import (RewardRecord, RewardStrategy,
                                                  RewardType)
    from svc.apps.marketing.repositories import (CampaignRepository,
                                                 InvitationRepository,
                                                 RewardRecordRepository,
                                                 RewardStrategyRepository)
    from svc.apps.products.repositories.category import CategoryRepository
    from svc.apps.products.repositories.inventory import InventoryRepository
    from svc.apps.products.repositories.product import ProductRepository
    from svc.apps.products.schemas.category import CategoryCreate
    from svc.apps.products.schemas.inventory import InventoryCreate
    from svc.apps.products.schemas.product import ProductCreate
    from svc.apps.shops.models.shop import ShopStatus
    from svc.apps.shops.repositories.shop import ShopRepository
    from svc.apps.shops.schemas.shop import ShopCreate
    from svc.core.database.session import get_session
    from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 创建日志
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('seed_data')

def run_command(command, check=True):
    """运行命令并打印输出"""
    print(f"执行命令: {command}")
    result = subprocess.run(command, shell=True, check=check)
    return result


def reset_database(recreate=False):
    """重置数据库
    Args:
        recreate: 是否重新创建数据库，如果为True，将删除并重新创建数据库；
                 如果为False，将只回滚并重新应用迁移
    """
    if recreate:
        # 获取数据库连接信息
        db_name = os.getenv("POSTGRES_DB", "fastapi_nano")
        db_user = os.getenv("POSTGRES_USER", "postgres")
        db_password = os.getenv("POSTGRES_PASSWORD", "postgres")
        db_server = os.getenv("POSTGRES_SERVER", "localhost")
        # 删除数据库
        print(f"删除数据库 {db_name}...")
        run_command(f"PGPASSWORD={db_password} dropdb -h {db_server} -U {db_user} {db_name}", check=False)
        # 创建数据库
        print(f"创建数据库 {db_name}...")
        run_command(f"PGPASSWORD={db_password} createdb -h {db_server} -U {db_user} {db_name}")
    else:
        # 回滚所有迁移
        print("回滚所有迁移...")
        run_command("alembic downgrade base")
    # 应用所有迁移
    print("应用所有迁移...")
    run_command("alembic upgrade head")
    print("数据库重置完成！")

async def create_permissions(db: AsyncSession) -> List[Permission]:
    """创建权限数据"""
    # 先检查是否已存在
    result = await db.execute(select(Permission).where(Permission.name == "user:read"))
    existing = result.scalars().first()
    if existing:
        logger.info("权限数据已存在，跳过创建")
        # 返回所有现有权限
        result = await db.execute(select(Permission))
        return result.scalars().all()
    
    logger.info("创建权限数据...")
    permissions_data = [
        # 全局权限
        {"name": "*:*", "description": "所有权限（超级管理员）"},
        
        # 认证授权模块权限
        {"name": "user:read", "description": "查看用户信息"},
        {"name": "user:create", "description": "创建用户"},
        {"name": "user:update", "description": "更新用户信息"},
        {"name": "user:delete", "description": "删除用户"},
        {"name": "user:manage", "description": "管理用户（禁用/启用账户等）"},
        {"name": "role:read", "description": "查看角色信息"},
        {"name": "role:create", "description": "创建角色"},
        {"name": "role:update", "description": "更新角色"},
        {"name": "role:delete", "description": "删除角色"},
        {"name": "role:assign", "description": "分配角色给用户"},
        {"name": "permission:read", "description": "查看权限信息"},
        {"name": "permission:assign", "description": "给角色分配权限"},
        
        # 计费/订阅模块权限
        {"name": "subscription_plan:read", "description": "查看订阅计划"},
        {"name": "subscription_plan:create", "description": "创建订阅计划"},
        {"name": "subscription_plan:update", "description": "更新订阅计划"},
        {"name": "subscription_plan:delete", "description": "删除订阅计划"},
        {"name": "subscription:read", "description": "查看订阅信息"},
        {"name": "subscription:create", "description": "创建订阅"},
        {"name": "subscription:update", "description": "更新订阅"},
        {"name": "subscription:cancel", "description": "取消订阅"},
        {"name": "subscription:renew", "description": "续订订阅"},
        {"name": "invoice:read", "description": "查看发票"},
        {"name": "invoice:create", "description": "创建发票"},
        {"name": "invoice:update", "description": "更新发票"},
        {"name": "invoice:delete", "description": "删除发票"},
        {"name": "payment:read", "description": "查看支付信息"},
        {"name": "payment:process", "description": "处理支付"},
        {"name": "payment:refund", "description": "退款操作"},
        
        # 营销模块权限
        {"name": "campaign:read", "description": "查看营销活动"},
        {"name": "campaign:create", "description": "创建营销活动"},
        {"name": "campaign:update", "description": "更新营销活动"},
        {"name": "campaign:delete", "description": "删除营销活动"},
        {"name": "campaign:activate", "description": "激活/停用营销活动"},
        {"name": "invitation:read", "description": "查看邀请信息"},
        {"name": "invitation:create", "description": "创建邀请"},
        {"name": "invitation:delete", "description": "删除邀请"},
        {"name": "reward_strategy:read", "description": "查看奖励策略"},
        {"name": "reward_strategy:create", "description": "创建奖励策略"},
        {"name": "reward_strategy:update", "description": "更新奖励策略"},
        {"name": "reward_strategy:delete", "description": "删除奖励策略"},
        {"name": "reward_record:read", "description": "查看奖励记录"},
        {"name": "reward_record:create", "description": "创建奖励记录"},
        {"name": "reward_record:issue", "description": "发放奖励"},
        
        # 系统模块权限
        {"name": "config:read", "description": "查看系统配置"},
        {"name": "config:update", "description": "更新系统配置"},
        {"name": "audit_log:read", "description": "查看审计日志"},
        {"name": "audit_log:delete", "description": "删除审计日志"},
        {"name": "system:backup", "description": "系统备份操作"},
        {"name": "system:restore", "description": "系统恢复操作"},
        {"name": "system:maintenance", "description": "系统维护模式切换"},
        {"name": "system:monitor", "description": "系统监控访问权限"}
    ]
    
    permissions = []
    for perm_data in permissions_data:
        perm = Permission(**perm_data)
        db.add(perm)
        permissions.append(perm)
    
    await db.commit()
    logger.info(f"创建了{len(permissions)}个权限")
    return permissions

async def create_roles(db: AsyncSession, permissions: List[Permission]) -> List[Role]:
    """创建角色数据"""
    # 先检查是否已存在
    result = await db.execute(select(Role).where(Role.name == "admin"))
    existing = result.scalars().first()
    if existing:
        logger.info("角色数据已存在，跳过创建")
        # 返回所有现有角色
        result = await db.execute(select(Role))
        return result.scalars().all()
    
    logger.info("创建角色数据...")
    roles_data = [
        {
            "name": "admin",
            "description": "系统管理员",
            "is_system": True,
            "permissions": permissions  # 管理员拥有所有权限
        },
        {
            "name": "user",
            "description": "普通用户",
            "is_system": True,
            "permissions": [p for p in permissions if p.name.startswith("user:read")]
        },
        {
            "name": "vip",
            "description": "VIP用户",
            "is_system": False,
            "permissions": [p for p in permissions if ":read" in p.name]
        }
    ]
    
    roles = []
    for role_data in roles_data:
        role_permissions = role_data.pop("permissions")
        role = Role(**role_data)
        role.permissions = role_permissions
        db.add(role)
        roles.append(role)
    
    await db.commit()
    logger.info(f"创建了{len(roles)}个角色")
    return roles

async def create_users(db: AsyncSession, roles: List[Role]) -> List[User]:
    """创建用户数据"""
    logger.info("开始创建用户数据...")
    users_data = [
        {
            "email": "<EMAIL>",
            "password": "qinjun666",
            "username": "super",
            "fullname": "系统管理员",
            "is_superuser": True,
            "roles": [roles[0]]  # admin角色
        },
        {
            "email": "<EMAIL>",
            "password": "user123",
            "username": "user1",
            "fullname": "测试用户1",
            "roles": [roles[1]]  # user角色
        },
        {
            "email": "<EMAIL>",
            "password": "user123",
            "username": "user2",
            "fullname": "测试用户2",
            "roles": [roles[1], roles[2]]  # user + vip角色
        }
    ]
    
    users = []
    for user_data in users_data:
        # 检查用户是否已存在
        email = user_data["email"]
        result = await db.execute(select(User).where(User.email == email))
        existing = result.scalars().first()
        
        if existing:
            logger.info(f"用户 {email} 已存在，跳过创建")
            users.append(existing)
            continue
        
        # 创建新用户
        user_roles = user_data.pop("roles")
        user = await UserRepository(db=db).create(
            **user_data
        )
        
        # 设置用户角色关系
        for role in user_roles:
            stmt = insert(user_role).values(user_id=user.id, role_id=role.id)
            await db.execute(stmt)
        
        logger.info(f"成功创建用户: {email}")
        users.append(user)
    
    await db.commit()
    logger.info(f"用户创建过程完成，共有 {len(users)} 个用户")
    return users

async def create_wechat_users(db: AsyncSession, users: List[User]) -> List[WechatUser]:
    """创建微信用户数据"""
    # 先检查是否已存在
    result = await db.execute(select(WechatUser).where(WechatUser.openid == "wx_openid_111111"))
    existing = result.scalars().first()
    if existing:
        logger.info("微信用户数据已存在，跳过创建")
        # 返回所有现有微信用户
        result = await db.execute(select(WechatUser))
        return result.scalars().all()
    
    logger.info("创建微信用户数据...")
    
    # 只为部分用户创建微信账号
    wechat_users_data = [
        {
            "openid": "wx_openid_111111",
            "unionid": "wx_unionid_111111",
            "nickname": "微信用户1",
            "avatar_url": "https://example.com/avatar1.jpg",
            "gender": 1,  # 男
            "country": "中国",
            "province": "广东",
            "city": "深圳",
            "language": "zh_CN",
            "user": users[1]  # user1
        },
        {
            "openid": "wx_openid_222222",
            "unionid": "wx_unionid_222222",
            "nickname": "微信用户2",
            "avatar_url": "https://example.com/avatar2.jpg",
            "gender": 2,  # 女
            "country": "中国",
            "province": "上海",
            "city": "上海",
            "language": "zh_CN",
            "user": users[2]  # user2
        }
    ]
    
    wechat_users = []
    for wx_data in wechat_users_data:
        user = wx_data.pop("user")
        wx_user = WechatUser(**wx_data)
        wx_user.user = user
        wx_user.user_id = user.id
        db.add(wx_user)
        wechat_users.append(wx_user)
    
    await db.commit()
    logger.info(f"创建了{len(wechat_users)}个微信用户")
    return wechat_users

async def create_subscription_plans(db: AsyncSession, creator_id: int) -> List[SubscriptionPlan]:
    """
    创建订阅计划数据
    
    Args:
        db: 数据库会话
        creator_id: 创建者ID，默认为1(管理员)
    
    Returns:
        List[SubscriptionPlan]: 创建的订阅计划列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(SubscriptionPlan).where(SubscriptionPlan.name == "基础版"))
        existing = result.scalars().first()
        if existing:
            logger.info("订阅计划数据已存在，跳过创建")
            # 返回所有现有订阅计划
            result = await db.execute(select(SubscriptionPlan))
            return result.scalars().all()
        
        # 验证creator_id是否存在
        user_check = await db.execute(select(User).where(User.id == creator_id))
        user = user_check.scalars().first()
        if not user:
            raise ValueError(f"用户ID {creator_id} 不存在，无法创建订阅计划")
        
        logger.info(f"创建订阅计划数据，创建者ID: {creator_id}...")
        
        plans_data = [
            {
                "name": "基础版",
                "description": "适合个人用户",
                "price": 99.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "basic",
                "max_users": 1,
                "max_storage": 1024,  # 1GB
                "max_projects": 3,
                "features": {"api_calls": 1000, "support": "email"},
                "user_id": creator_id
            },
            {
                "name": "专业版",
                "description": "适合小型团队",
                "price": 299.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "premium",
                "max_users": 5,
                "max_storage": 5120,  # 5GB
                "max_projects": 10,
                "features": {"api_calls": 5000, "support": "priority"},
                "user_id": creator_id
            },
            {
                "name": "企业版",
                "description": "适合大型企业",
                "price": 999.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "enterprise",
                "max_users": 20,
                "max_storage": 20480,  # 20GB
                "max_projects": 50,
                "features": {"api_calls": "unlimited", "support": "24/7"},
                "user_id": creator_id
            }
        ]
        
        plans = []
        plan_repo = SubscriptionPlanRepository(db=db)
        for idx, plan_data in enumerate(plans_data, 1):
            try:
                # 使用仓库模式创建订阅计划，确保数据一致性
                plan = await plan_repo.create(
                    user_id=plan_data["user_id"],
                    name=plan_data["name"],
                    description=plan_data.get("description"),
                    price=plan_data["price"],
                    currency=plan_data.get("currency", "CNY"),
                    interval=plan_data.get("interval", "month"),
                    features=plan_data.get("features", {}),
                    tier=plan_data.get("tier"),
                    max_users=plan_data.get("max_users"),
                    max_storage=plan_data.get("max_storage"),
                    max_projects=plan_data.get("max_projects")
                )
                logger.info(f"创建订阅计划 {idx}/{len(plans_data)}: {plan.name} (ID: {plan.id})")
                plans.append(plan)
            except Exception as e:
                logger.error(f"创建订阅计划 '{plan_data['name']}' 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(plans)}个订阅计划")
        return plans
    except Exception as e:
        logger.error(f"创建订阅计划时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_campaigns(session: AsyncSession, admin_user_id: int) -> list[Campaign]:
    """
    创建营销活动。
    """
    logging.info("开始创建营销活动...")
    try:
        campaign_repo = CampaignRepository(db=session)
        # 检查是否已存在
        campaigns, total = await campaign_repo.get_campaigns()
        if total > 0:
            logging.info(f"营销活动已存在（共{total}个），跳过创建")
            return campaigns
        campaign_ids = []
        
        # 创建邀请新用户活动
        invite_campaign = Campaign(
            name="邀请新用户",
            description="邀请新用户注册并使用我们的服务",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=180),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(invite_campaign)
        await session.flush()
        campaign_ids.append(invite_campaign.id)
        logging.info(f"创建营销活动成功: {invite_campaign.name}, ID: {invite_campaign.id}")
        
        # 创建首次购买奖励活动
        first_purchase_campaign = Campaign(
            name="首次购买奖励",
            description="奖励首次在平台上购买的用户",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=90),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(first_purchase_campaign)
        await session.flush()
        campaign_ids.append(first_purchase_campaign.id)
        logging.info(f"创建营销活动成功: {first_purchase_campaign.name}, ID: {first_purchase_campaign.id}")
        
        # 创建VIP会员专享活动
        vip_campaign = Campaign(
            name="VIP会员专享",
            description="VIP会员专享活动与优惠",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=120),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(vip_campaign)
        await session.flush()
        campaign_ids.append(vip_campaign.id)
        logging.info(f"创建营销活动成功: {vip_campaign.name}, ID: {vip_campaign.id}")
        
        logging.info(f"成功创建 {len(campaign_ids)} 个营销活动")
        return campaign_ids
    except Exception as e:
        logging.error(f"创建营销活动失败: {str(e)}")
        raise

async def create_reward_strategies(db: AsyncSession, campaigns: List[int], creator_id: int) -> List[RewardStrategy]:
    """
    创建奖励策略数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        creator_id: 创建者ID
    
    Returns:
        List[RewardStrategy]: 创建的奖励策略列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(RewardStrategy).where(RewardStrategy.name == "邀请人奖励"))
        existing = result.scalars().first()
        if existing:
            logger.info("奖励策略数据已存在，跳过创建")
            # 返回所有现有策略
            result = await db.execute(select(RewardStrategy))
            return result.scalars().all()
        
        if not campaigns or len(campaigns) < 3:
            raise ValueError(f"需要至少3个营销活动来创建奖励策略，当前只有{len(campaigns) if campaigns else 0}个")
        
        logger.info("创建奖励策略数据...")
        
        # 初始化策略仓库
        reward_strategy_repo = RewardStrategyRepository(db=db)
        
        # 为每个活动创建奖励策略
        reward_strategies_data = [
            # 活动1的策略
            {
                "campaign_id": campaigns[0],
                "name": "邀请人奖励",
                "description": "邀请好友注册成功后，邀请人获得的奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": True,
                "is_for_invitee": False,
                "base_reward": 50.0,  # 固定奖励50元
                "min_invitations": 1,
                "max_rewards": 5
            },
            {
                "campaign_id": campaigns[0],
                "name": "受邀人奖励",
                "description": "被邀请注册成功后，受邀人获得的奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": False,
                "is_for_invitee": True,
                "base_reward": 20.0,  # 固定奖励20元
                "min_invitations": None,
                "max_rewards": 1
            },
            # 活动2的策略
            {
                "campaign_id": campaigns[1],
                "name": "春季推广邀请奖励",
                "description": "春季推广活动邀请奖励",
                "reward_type": RewardType.TIERED,
                "is_for_inviter": True,
                "is_for_invitee": False,
                "base_reward": 30.0,
                "tiered_config": json.dumps([
                    {"threshold": 3, "reward": 50.0},
                    {"threshold": 5, "reward": 100.0},
                    {"threshold": 10, "reward": 200.0}
                ]),
                "min_invitations": 1,
                "max_rewards": 10
            },
            {
                "campaign_id": campaigns[1],
                "name": "春季推广受邀奖励",
                "description": "春季推广活动受邀奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": False,
                "is_for_invitee": True,
                "base_reward": 30.0,
                "min_invitations": None,
                "max_rewards": 1
            },
            # 活动3的策略
            {
                "campaign_id": campaigns[2],
                "name": "VIP会员专享奖励",
                "description": "VIP专享活动奖励",
                "reward_type": RewardType.PERCENTAGE,
                "is_for_inviter": True,
                "is_for_invitee": True,
                "base_reward": 10.0,
                "percentage_rate": 15.0,  # 15%的额外奖励
                "min_invitations": 1,
                "max_rewards": 3
            }
        ]
        
        reward_strategies = []
        for idx, strategy_data in enumerate(reward_strategies_data, 1):
            try:
                # 验证campaign_id
                campaign_id = strategy_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"策略 '{strategy_data['name']}' 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                # 使用RewardStrategyRepository创建策略
                strategy = await reward_strategy_repo.create_strategy(
                    campaign_id=strategy_data["campaign_id"],
                    name=strategy_data["name"],
                    description=strategy_data.get("description"),
                    reward_type=strategy_data["reward_type"],
                    is_for_inviter=strategy_data["is_for_inviter"],
                    is_for_invitee=strategy_data["is_for_invitee"],
                    base_reward=strategy_data["base_reward"],
                    percentage_rate=strategy_data.get("percentage_rate"),
                    tiered_config=strategy_data.get("tiered_config"),
                    min_invitations=strategy_data.get("min_invitations"),
                    max_rewards=strategy_data.get("max_rewards")
                )
                
                logger.info(f"创建奖励策略 {idx}/{len(reward_strategies_data)}: {strategy.name} (ID: {strategy.id})")
                reward_strategies.append(strategy)
            except Exception as e:
                logger.error(f"创建奖励策略 '{strategy_data['name']}' 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(reward_strategies)}个奖励策略")
        return reward_strategies
    except Exception as e:
        logger.error(f"创建奖励策略时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_invitations(db: AsyncSession, campaigns: List[Campaign], users: List[User]) -> List[Invitation]:
    """
    创建邀请记录数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        users: 用户列表
    
    Returns:
        List[Invitation]: 创建的邀请记录列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(Invitation).where(Invitation.code == "INV123456"))
        existing = result.scalars().first()
        if existing:
            logger.info("邀请记录数据已存在，跳过创建")
            # 返回所有现有邀请
            result = await db.execute(select(Invitation))
            return result.scalars().all()
        
        # 验证输入数据
        if not campaigns or len(campaigns) < 2:
            raise ValueError(f"需要至少2个营销活动来创建邀请记录，当前只有{len(campaigns) if campaigns else 0}个")
        
        if not users or len(users) < 3:
            raise ValueError(f"需要至少3个用户来创建邀请记录，当前只有{len(users) if users else 0}个")
        
        logger.info("创建邀请记录数据...")
        
        now = get_utc_now_without_tzinfo()
        
        # 创建邀请记录
        invitations_data = [
            # user1邀请user2(已使用)
            {
                "campaign_id": campaigns[0],
                "inviter_id": users[1].id,  # user1
                "invitee_id": users[2].id,  # user2
                "code": "INV123456",
                "is_used": True,
                "used_at": now - timedelta(days=15),
                "invitee_ip": "*************",
                "invitee_device": "iPhone 13",
                "opened_count": 2
            },
            # user2邀请其他用户(未使用)
            {
                "campaign_id": campaigns[0],
                "inviter_id": users[2].id,  # user2
                "invitee_id": None,  # 未被使用
                "code": "INV234567",
                "is_used": False,
                "used_at": None,
                "invitee_ip": None,
                "invitee_device": None,
                "opened_count": 5
            },
            # user1在另一个活动中的邀请
            {
                "campaign_id": campaigns[1],
                "inviter_id": users[1].id,  # user1
                "invitee_id": None,  # 未被使用
                "code": "SPR123456",
                "is_used": False,
                "used_at": None,
                "invitee_ip": None,
                "invitee_device": None,
                "opened_count": 3
            }
        ]
        
        invitations = []
        for idx, invitation_data in enumerate(invitations_data, 1):
            try:
                # 验证外键ID是否存在
                campaign_id = invitation_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"邀请记录 #{idx} 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                inviter_id = invitation_data["inviter_id"]
                inviter_check = await db.execute(select(User).where(User.id == inviter_id))
                if not inviter_check.scalars().first():
                    logger.warning(f"邀请记录 #{idx} 引用的邀请人ID {inviter_id} 不存在，跳过创建")
                    continue
                
                # 如果有被邀请人，也检查ID
                invitee_id = invitation_data["invitee_id"]
                if invitee_id is not None:
                    invitee_check = await db.execute(select(User).where(User.id == invitee_id))
                    if not invitee_check.scalars().first():
                        logger.warning(f"邀请记录 #{idx} 引用的被邀请人ID {invitee_id} 不存在，跳过创建")
                        continue
                
                invitation = Invitation(**invitation_data)
                db.add(invitation)
                await db.flush()
                logger.info(f"创建邀请记录 {idx}/{len(invitations_data)}: {invitation.code} (ID: {invitation.id})")
                invitations.append(invitation)
            except Exception as e:
                logger.error(f"创建邀请记录 #{idx} 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(invitations)}个邀请记录")
        return invitations
    except Exception as e:
        logger.error(f"创建邀请记录时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_reward_records(db: AsyncSession, campaigns: List[Campaign], users: List[User], invitations: List[Invitation], reward_strategies: List[RewardStrategy]) -> List[RewardRecord]:
    """
    创建奖励记录数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        users: 用户列表
        invitations: 邀请记录列表
        reward_strategies: 奖励策略列表
    
    Returns:
        List[RewardRecord]: 创建的奖励记录列表
    """
    try:
        # 先检查是否已存在
        if not invitations or not invitations[0]:
            raise ValueError("缺少邀请记录，无法创建奖励记录")
        
        # 如果至少有一个邀请记录，检查是否已存在奖励记录
        check_invitation = invitations[0]
        result = await db.execute(select(RewardRecord).where(
            (RewardRecord.campaign_id == check_invitation.campaign_id) & 
            (RewardRecord.user_id == check_invitation.inviter_id)
        ))
        existing = result.scalars().first()
        if existing:
            logger.info("奖励记录数据已存在，跳过创建")
            # 返回所有现有奖励记录
            result = await db.execute(select(RewardRecord))
            return result.scalars().all()
        
        # 验证输入数据
        if not campaigns or len(campaigns) < 2:
            raise ValueError(f"需要至少2个营销活动来创建奖励记录，当前只有{len(campaigns) if campaigns else 0}个")
        
        if not users or len(users) < 2:
            raise ValueError(f"需要至少2个用户来创建奖励记录，当前只有{len(users) if users else 0}个")
        
        if not reward_strategies or len(reward_strategies) < 3:
            raise ValueError(f"需要至少3个奖励策略来创建奖励记录，当前只有{len(reward_strategies) if reward_strategies else 0}个")
        
        logger.info("创建奖励记录数据...")
        
        now = get_utc_now_without_tzinfo()
        
        # 获取已使用的邀请
        used_invitation = None
        for inv in invitations:
            if inv.is_used:
                used_invitation = inv
                break
        
        if not used_invitation:
            logger.warning("没有找到已使用的邀请记录，将使用第一个邀请记录")
            used_invitation = invitations[0]
        
        reward_records_data = [
            # 邀请人的奖励(已发放)
            {
                "campaign_id": used_invitation.campaign_id,
                "invitation_id": used_invitation.id,
                "strategy_id": reward_strategies[0].id,  # 对应"邀请人奖励"策略
                "user_id": used_invitation.inviter_id,
                "reward_type": "cash",
                "reward_value": 50.0,
                "reward_description": "成功邀请好友注册",
                "is_issued": True,
                "issued_at": now - timedelta(days=14)
            },
            # 被邀请人的奖励(已发放)
            {
                "campaign_id": used_invitation.campaign_id,
                "invitation_id": used_invitation.id,
                "strategy_id": reward_strategies[1].id,  # 对应"受邀人奖励"策略
                "user_id": used_invitation.invitee_id if used_invitation.invitee_id else users[2].id,
                "reward_type": "cash",
                "reward_value": 20.0,
                "reward_description": "接受邀请注册",
                "is_issued": True,
                "issued_at": now - timedelta(days=14)
            },
            # 另一个活动中的奖励(未发放)
            {
                "campaign_id": campaigns[1],
                "invitation_id": None,
                "strategy_id": reward_strategies[2].id,  # 对应"春季推广邀请奖励"策略
                "user_id": users[1].id,  # user1
                "reward_type": "cash",
                "reward_value": 30.0,
                "reward_description": "春季活动特别奖励",
                "is_issued": False,
                "issued_at": None
            }
        ]
        
        reward_records = []
        for idx, record_data in enumerate(reward_records_data, 1):
            try:
                # 验证外键ID
                campaign_id = record_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                if record_data["invitation_id"]:
                    invitation_id = record_data["invitation_id"]
                    invitation_check = await db.execute(select(Invitation).where(Invitation.id == invitation_id))
                    if not invitation_check.scalars().first():
                        logger.warning(f"奖励记录 #{idx} 引用的邀请ID {invitation_id} 不存在，跳过创建")
                        continue
                
                strategy_id = record_data["strategy_id"]
                strategy_check = await db.execute(select(RewardStrategy).where(RewardStrategy.id == strategy_id))
                if not strategy_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的策略ID {strategy_id} 不存在，跳过创建")
                    continue
                
                user_id = record_data["user_id"]
                user_check = await db.execute(select(User).where(User.id == user_id))
                if not user_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的用户ID {user_id} 不存在，跳过创建")
                    continue
                
                record = RewardRecord(**record_data)
                db.add(record)
                await db.flush()
                logger.info(f"创建奖励记录 {idx}/{len(reward_records_data)}: {record.reward_description} (ID: {record.id})")
                reward_records.append(record)
            except Exception as e:
                logger.error(f"创建奖励记录 #{idx} 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(reward_records)}个奖励记录")
        return reward_records
    except Exception as e:
        logger.error(f"创建奖励记录时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_albums(db: AsyncSession) -> List:
    """创建图册数据"""
    try:
        repo = AlbumRepository(db)
        # 检查是否已存在
        albums, total = await repo.get_albums()
        if total > 0:
            logger.info("图册数据已存在，跳过创建")
            return albums
        logger.info("创建图册数据...")
        albums_data = [
            AlbumCreate(
                name="主图册",
                description="商品主图",
                tags=["main", "product"],
                cover_image_id=None,
                status="active",
                sort_order=1,
                meta_data={"scene": "product"}
            ),
            AlbumCreate(
                name="活动图册",
                description="营销活动相关图片",
                tags=["marketing"],
                cover_image_id=None,
                status="active",
                sort_order=2,
                meta_data={"scene": "marketing"}
            )
        ]
        created = []
        for album in albums_data:
            created_album = await repo.create(album.model_dump())
            created.append(created_album)
            logger.info(f"创建图册: {album.name}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个图册")
        return created
    except Exception as e:
        logger.error(f"创建图册时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_categories(db: AsyncSession) -> list:
    """创建头盔分类数据（6类）"""
    try:
        repo = CategoryRepository(db)
        categories, total = await repo.get_categories()
        # 检查是否已存在6个头盔分类
        helmet_types = [
            "安全帽", "骑行盔", "摩托盔", "滑雪盔", "攀岩盔", "马术盔"
        ]
        exist_names = [cat.name for cat in categories]
        to_create = [name for name in helmet_types if name not in exist_names]
        created = []
        for idx, name in enumerate(to_create):
            cat = await repo.create(CategoryCreate(
                name=name,
                description=f"{name}类头盔产品",
                slug=f"helmet-{idx+1}",
            ).model_dump())
            created.append(cat)
            logger.info(f"创建头盔分类: {name}")
        await db.commit()
        # 重新获取所有分类，返回头盔分类对象列表
        all_cats, _ = await repo.get_categories()
        helmet_cats = [cat for cat in all_cats if cat.name in helmet_types]
        logger.info(f"成功创建/获取{len(helmet_cats)}个头盔分类")
        return helmet_cats
    except Exception as e:
        logger.error(f"创建分类时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_products(db: AsyncSession, categories: list) -> List:
    """批量创建头盔产品数据（6类，每类5个），每个产品专属图册和PNG透明底图片，适配移动端"""
    try:
        repo = ProductRepository(db)
        products, total = await repo.get_products()
        if total > 0:
            logger.info("商品数据已存在，跳过创建")
            return products
        logger.info("批量创建头盔产品数据...")
        # 公开PNG透明底图片库
        png_image_urls = [
            "http://res.yqbaijiu.com/20250626/dc43232763b24acf8012fdbe392277d0.jpg",
            "http://res.yqbaijiu.com/20250626/80953d93b119496a9b70684ed12c9732.jpg",
            "http://res.yqbaijiu.com/20250626/693fa52621204022b05b7e92baeba619.jpg",
            "http://res.yqbaijiu.com/20250626/dce92a01ccf64421939b9c7ae8572e69.jpg",
            "http://res.yqbaijiu.com/20250626/5cfbb8b5a67647c8a8b2231fe3a13bdc.jpg",
            "http://res.yqbaijiu.com/20250626/06f1f9db12ff47a9837b2ad089f63a3f.jpg",
            "http://res.yqbaijiu.com/20250626/ac326a6f919a40788e10328d07048ea5.jpg",
            "http://res.yqbaijiu.com/20250626/35d60a1e25744865adac3f179e7a1cd1.jpg",
            "http://res.yqbaijiu.com/20250626/90d9d57057f54dcea3898f0b2984b0fe.jpg",
        ]
        created = []
        album_repo = AlbumRepository(db)
        album_image_repo = AlbumImageRepository(db)
        for idx, cat in enumerate(categories):
            helmet_type = cat.name
            for i in range(5):
                product_name = f"{helmet_type}{i+1}"
                sku = f"{helmet_type[:2].upper()}{i+1:02d}"
                # 创建专属图册
                album = await album_repo.create(AlbumCreate(
                    name=f"{product_name}图册",
                    description=f"{product_name}的主图册",
                    tags=[helmet_type, "头盔"],
                    status="active",
                    sort_order=idx*5+i+1,
                    meta_data={"scene": "product"}
                ).model_dump())
                # 为图册添加1~3张PNG透明底图片
                num_images = random.randint(1, 3)
                cover_image_id = None
                for j in range(num_images):
                    url = random.choice(png_image_urls)
                    img = await album_image_repo.create(AlbumImageCreate(
                        album_id=album.id,
                        url=url,
                        file_name=url.split("/")[-1],
                        file_size=0,
                        width=800,
                        height=800,
                        mime_type="image/png",
                        is_cover=(j==0),
                        sort_order=j+1,
                        status="active",
                        meta_data={"scene": "product"}
                    ).model_dump())
                    if j == 0:
                        cover_image_id = img.id
                # 设置封面ID
                if cover_image_id:
                    album.cover_image_id = cover_image_id
                    db.add(album)
                # 创建产品
                product = await repo.create_with_album(ProductCreate(
                    name=product_name,
                    description=f"{helmet_type}高品质防护，适合多场景使用。",
                    short_description=f"优质{helmet_type}，安全舒适。",
                    sku=sku,
                    barcode=f"{sku}BAR{i+1:03d}",
                    category_id=cat.id,
                    price=199.0 + idx*20 + i*5,
                    cost_price=120.0 + idx*10,
                    market_price=299.0 + idx*30,
                    currency="CNY",
                    is_featured=(i==0),
                    is_digital=False,
                    track_inventory=True,
                    stock_quantity=50 + i*10,
                    min_stock_level=5,
                    max_stock_level=200,
                    weight=0.45 + idx*0.05,
                    dimensions={"length": 25+idx, "width": 20+idx, "height": 15+idx},
                    attributes={"类型": helmet_type},
                    seo_title=f"{product_name} - 专业{helmet_type}商城",
                    seo_description=f"{helmet_type}，安全舒适，适合骑行、施工等多场景。",
                    seo_keywords=f"{helmet_type},头盔,安全,防护",
                    rich_description=f'<p>{helmet_type}，高品质防护，适合多场景使用。</p>'
                ), album.id)
                created.append(product)
                logger.info(f"创建头盔产品: {product_name}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个头盔产品")
        return created
    except Exception as e:
        logger.error(f"创建头盔产品时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_shops(db: AsyncSession) -> List:
    """批量创建10个门店，每个门店专属图册和图片（随机网络图片）"""
    try:
        repo = ShopRepository(db)
        from svc.apps.shops.schemas.shop import GetShopsParams
        params = GetShopsParams(page_num=1, page_size=1)
        shops, total = await repo.get_shops(params)
        if total >= 10:
            logger.info("门店数据已存在，跳过创建")
            return shops
        logger.info("批量创建门店数据...")
        album_repo = AlbumRepository(db)
        album_image_repo = AlbumImageRepository(db)
        shop_names = [f"测试门店{i+1}" for i in range(10)]
        created = []
        for idx, name in enumerate(shop_names):
            shop = await repo.create(ShopCreate(
                name=name,
                description=f"{name}，优质服务，欢迎光临！",
                address_line1=f"测试路{100+idx}号",
                address_line2=None,
                city="测试市",
                state_province="测试省",
                postal_code=f"1000{idx}",
                country="CN",
                phone_number=f"010-8888{1000+idx}",
                email=f"shop{idx+1}@example.com",
                website=f"https://shop.example.com/{idx+1}",
                opening_hours={"Mon-Sun": "9am-9pm"},
                is_franchise=bool(idx%2),
                latitude=str(30.0+idx*0.1),
                longitude=str(120.0+idx*0.1),
                extra_info={"floor_area_sqm": 100+idx*10, "has_parking": bool(idx%2)},
                status=ShopStatus.OPEN
            ).model_dump())
            # 创建专属图册
            album = await album_repo.create(AlbumCreate(
                name=f"{name}图册",
                description=f"{name}的主图册",
                tags=["门店"],
                status="active",
                sort_order=idx+1,
                meta_data={"scene": "shop"}
            ).model_dump())
            # 关联图册到店铺
            shop.album_id = album.id
            db.add(shop)
            # 添加1~3张随机网络图片
            num_images = random.randint(1, 3)
            cover_image_id = None
            for j in range(num_images):
                url = f"https://picsum.photos/seed/{random.randint(1000,9999)}/800/800"
                created_image = await album_image_repo.create(AlbumImageCreate(
                    album_id=album.id,
                    url=url,
                    file_name=f"shop_{idx+1}_img{j+1}.jpg",
                    file_size=0,
                    width=800,
                    height=800,
                    mime_type="image/jpeg",
                    is_cover=(j==0),
                    sort_order=j+1,
                    status="active",
                    meta_data={"scene": "shop"}
                ).model_dump())
                if j == 0:
                    cover_image_id = created_image.id
            
            # 更新相册的封面ID
            if cover_image_id:
                album.cover_image_id = cover_image_id
                db.add(album)

            created.append(shop)
            logger.info(f"创建门店: {name}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个门店")
        return created
    except Exception as e:
        logger.error(f"创建门店时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_inventories(db: AsyncSession, products: list, variants: list) -> list:
    """为每个商品和变体创建库存"""
    try:
        repo = InventoryRepository(db)
        created = []
        # 商品库存
        for product in products:
            invs, _ = await repo.get_by_product_id(product.id)
            if invs:
                logger.info(f"商品 {product.name} 已有库存，跳过")
                created.extend(invs)
                continue
            inv = InventoryCreate(
                product_id=product.id,
                variant_id=None,
                quantity=product.stock_quantity,
                available_quantity=product.stock_quantity,
                status="available"
            )
            created_inv = await repo.create(inv.model_dump())
            created.append(created_inv)
            logger.info(f"创建商品库存: {product.name}")
        # 变体库存
        for variant in variants:
            invs, _ = await repo.get_by_variant_id(variant.id)
            if invs:
                logger.info(f"变体 {variant.name} 已有库存，跳过")
                created.extend(invs)
                continue
            inv = InventoryCreate(
                product_id=variant.product_id,
                variant_id=variant.id,
                quantity=variant.stock_quantity,
                available_quantity=variant.stock_quantity,
                status="available"
            )
            created_inv = await repo.create(inv.model_dump())
            created.append(created_inv)
            logger.info(f"创建变体库存: {variant.name}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个库存记录")
        return created
    except Exception as e:
        logger.error(f"创建库存时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_album_images(db: AsyncSession, albums: list) -> list:
    """为每个图册创建图片，url随机"""
    try:
        repo = AlbumImageRepository(db)
        created = []
        for album in albums:
            images, _ = await repo.get_album_images(album_id=album.id)
            if images:
                logger.info(f"图册 {album.name} 已有图片，跳过")
                created.extend(images)
                continue
            for i in range(2):
                url = f"https://picsum.photos/seed/{random.randint(1000,9999)}/800/600"
                img = AlbumImageCreate(
                    album_id=album.id,
                    url=url,
                    file_name=f"image_{i+1}.jpg",
                    file_size=102400,
                    width=800,
                    height=600,
                    mime_type="image/jpeg",
                    is_cover=(i==0),
                    sort_order=i+1,
                    status="active",
                    meta_data={}
                )
                created_img = await repo.create(img.model_dump())
                created.append(created_img)
                logger.info(f"为图册 {album.name} 创建图片: {url}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个图册图片")
        return created
    except Exception as e:
        logger.error(f"创建图册图片时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_product_specs(db: AsyncSession, products: list) -> None:
    """
    为每个商品创建规格、规格值，并生成所有规格组合
    """
    from svc.apps.albums.services.album import AlbumService
    from svc.apps.products.repositories.specs import (
        ProductSpecCombinationRepository, ProductSpecOptionRepository,
        ProductSpecRepository, SpecOptionRepository, SpecRepository)
    from svc.apps.products.schemas.specs import (ProductSpecCombinationCreate,
                                                 ProductSpecCreate,
                                                 ProductSpecOptionCreate,
                                                 SpecCreate, SpecOptionCreate)
    from svc.apps.products.services.specs import ProductSpecCombinationService

    spec_repo = SpecRepository(db)
    option_repo = SpecOptionRepository(db)
    product_spec_repo = ProductSpecRepository(db)
    product_spec_option_repo = ProductSpecOptionRepository(db)
    combination_repo = ProductSpecCombinationRepository(db)
    album_service = AlbumService(db)

    album_repo = AlbumRepository(db)
    album_image_repo = AlbumImageRepository(db)

    # 全局唯一规格，先查找或新建
    async def get_or_create_spec(name: str):
        spec = await spec_repo.get_one(name=name)
        if spec:
            return spec
        return await spec_repo.create(SpecCreate(name=name, options=[]).model_dump())

    async def get_or_create_option(spec_id: int, value: str):
        opt = await option_repo.get_one(spec_id=spec_id, value=value)
        if opt:
            return opt
        return await option_repo.create(SpecOptionCreate(spec_id=spec_id, value=value).model_dump())

    for product in products:
        # 已存在校验：如该商品已有关联的规格，则跳过
        exist_specs = await product_spec_repo.list_by_product(product.id)
        if exist_specs:
            logger.info(f"商品 {product.name} 已存在规格，跳过规格/规格值/组合创建")
            continue
        # 全局唯一规格
        size_spec = await get_or_create_spec("尺寸")
        color_spec = await get_or_create_spec("颜色")
        material_spec = await get_or_create_spec("材质")
        # 规格值同理
        size_options = [
            await get_or_create_option(size_spec.id, "S"),
            await get_or_create_option(size_spec.id, "M"),
            await get_or_create_option(size_spec.id, "L"),
        ]
        color_options = [
            await get_or_create_option(color_spec.id, "玄青"),
            await get_or_create_option(color_spec.id, "朱砂"),
            await get_or_create_option(color_spec.id, "月白"),
            await get_or_create_option(color_spec.id, "石青"),
            await get_or_create_option(color_spec.id, "黛绿"),
        ]
        material_options = [
            await get_or_create_option(material_spec.id, "碳纤维"),
            await get_or_create_option(material_spec.id, "ABS"),
        ]
        # 商品与规格关联
        size_product_spec = await product_spec_repo.create(ProductSpecCreate(product_id=product.id, spec_id=size_spec.id).model_dump())
        color_product_spec = await product_spec_repo.create(ProductSpecCreate(product_id=product.id, spec_id=color_spec.id).model_dump())
        material_product_spec = await product_spec_repo.create(ProductSpecCreate(product_id=product.id, spec_id=material_spec.id).model_dump())
        # 商品与规格值关联
        for opt in size_options:
            await product_spec_option_repo.create(ProductSpecOptionCreate(product_spec_id=size_product_spec.id, spec_option_id=opt.id).model_dump())
        for opt in color_options:
            await product_spec_option_repo.create(ProductSpecOptionCreate(product_spec_id=color_product_spec.id, spec_option_id=opt.id).model_dump())
        for opt in material_options:
            await product_spec_option_repo.create(ProductSpecOptionCreate(product_spec_id=material_product_spec.id, spec_option_id=opt.id).model_dump())
        # 生成所有规格组合（笛卡尔积）
        option_id_lists = [
            [opt.id for opt in size_options],
            [opt.id for opt in color_options],
            [opt.id for opt in material_options],
        ]
        comb_service = ProductSpecCombinationService(combination_repo, album_service)
        all_combinations_result = comb_service.generate_combinations(option_id_lists)
        if not all_combinations_result.is_success:
            raise Exception(f"生成规格组合失败: {all_combinations_result.message}")
        all_combinations = all_combinations_result.data
        for idx, option_ids in enumerate(all_combinations):
            sku = f"{product.sku}-{'-'.join(str(i) for i in option_ids)}"
            price = product.price + idx * 100  # 不同组合价格略有差异
            stock_quantity = 10 + idx * 5
            # 直接用repo创建图册
            album = await album_repo.create(AlbumCreate(
                name=f"{product.name}-组合{idx+1}图册",
                description=f"{product.name}的第{idx+1}个规格组合图册",
                tags=["规格组合", product.name],
                status="active",
                sort_order=idx+1,
                meta_data={"scene": "product_combination"}
            ))
            album_id = album.id
            # 为图册添加1~2张图片
            num_images = random.randint(1, 2)
            for j in range(num_images):
                url = f"https://picsum.photos/seed/{random.randint(10000,99999)}/800/800"
                await album_image_repo.create(AlbumImageCreate(
                    album_id=album_id,
                    url=url,
                    file_name=f"comb_{sku}_img{j+1}.jpg",
                    file_size=0,
                    width=800,
                    height=800,
                    mime_type="image/jpeg",
                    is_cover=(j==0),
                    sort_order=j+1,
                    status="active",
                    meta_data={"scene": "product_combination"}
                ).model_dump())
            # 创建规格组合并关联album_id
            await combination_repo.create(ProductSpecCombinationCreate(
                product_id=product.id,
                sku=sku,
                price=price,
                stock_quantity=stock_quantity,
                spec_option_ids=option_ids,
                album_id=album_id
            ).model_dump())
    await db.commit()
    logger.info("成功为所有商品创建规格、规格值和规格组合")

async def main():
    """主函数，初始化所有数据"""
    try:
        # 创建数据库会话
        async with get_session() as db:
            logger.info("开始初始化数据...")
            permissions = await create_permissions(db)
            roles = await create_roles(db, permissions)
            users = await create_users(db, roles)

            subscription_plans = await create_subscription_plans(db, users[0].id)
            campaigns = await create_campaigns(db, users[0].id)
            reward_strategies = await create_reward_strategies(db, campaigns, users[0].id)
            invitations = await create_invitations(db, campaigns, users)
            reward_records = await create_reward_records(db, campaigns, users, invitations, reward_strategies)
            # 分类
            categories = await create_categories(db)
            # 图册
            albums = await create_albums(db)
            # 图库图片
            album_images = await create_album_images(db, albums)
            # 商品，归属头盔分类
            products = await create_products(db, categories)
            # 新增：为商品创建规格、规格值和规格组合
            await create_product_specs(db, products)
            # 门店
            await create_shops(db)
            logger.info("数据初始化完成！")
    except Exception as e:
        logger.error(f"初始化数据时发生错误: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="重置数据库并初始化种子数据")
    parser.add_argument("--recreate", action="store_true", help="是否重新创建数据库（dropdb+createdb）")
    parser.add_argument("--reset", action="store_true", help="是否重置数据库（回滚并升级迁移）")
    args = parser.parse_args()

    if args.recreate:
        reset_database(recreate=True)
    elif args.reset:
        reset_database(recreate=False)
    # 执行种子数据初始化
    asyncio.run(main()) 