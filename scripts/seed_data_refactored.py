#!/usr/bin/env python3
"""
重构后的种子数据脚本
解决循环导入问题，提高可维护性和性能

特点：
1. 模块化设计，按功能拆分
2. 延迟导入，避免循环依赖
3. 异步并发，提高执行效率
4. 错误隔离，单个模块失败不影响其他模块
"""
import argparse
import asyncio
import logging
import os
import subprocess
from typing import Dict, List, Optional

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('seed_data')


class DatabaseManager:
    """数据库管理器"""
    
    @staticmethod
    def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
        """运行命令并打印输出"""
        logger.info(f"执行命令: {command}")
        return subprocess.run(command, shell=True, check=check)
    
    @staticmethod
    def reset_database(recreate: bool = False) -> None:
        """重置数据库"""
        if recreate:
            # 获取数据库连接信息
            db_name = os.getenv("POSTGRES_DB", "fastapi_nano")
            db_user = os.getenv("POSTGRES_USER", "postgres")
            db_password = os.getenv("POSTGRES_PASSWORD", "postgres")
            db_server = os.getenv("POSTGRES_SERVER", "localhost")
            
            # 删除数据库
            logger.info(f"删除数据库 {db_name}...")
            DatabaseManager.run_command(
                f"PGPASSWORD={db_password} dropdb -h {db_server} -U {db_user} {db_name}",
                check=False
            )
            
            # 创建数据库
            logger.info(f"创建数据库 {db_name}...")
            DatabaseManager.run_command(
                f"PGPASSWORD={db_password} createdb -h {db_server} -U {db_user} {db_name}"
            )
        else:
            # 回滚所有迁移
            logger.info("回滚所有迁移...")
            DatabaseManager.run_command("alembic downgrade base")
        
        # 应用所有迁移
        logger.info("应用所有迁移...")
        DatabaseManager.run_command("alembic upgrade head")
        logger.info("数据库重置完成！")


class SeedDataModule:
    """种子数据模块基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f'seed_data.{name}')
    
    async def create_data(self, db, **kwargs) -> Dict:
        """创建种子数据 - 子类需要实现"""
        raise NotImplementedError
    
    async def run(self, db, **kwargs) -> Dict:
        """运行种子数据创建"""
        try:
            self.logger.info(f"开始创建 {self.name} 种子数据...")
            result = await self.create_data(db, **kwargs)
            self.logger.info(f"✅ {self.name} 种子数据创建完成")
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"❌ {self.name} 种子数据创建失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}


class AuthModule(SeedDataModule):
    """认证模块种子数据"""
    
    def __init__(self):
        super().__init__("auth")
    
    async def create_data(self, db, **kwargs) -> Dict:
        """创建认证相关数据"""
        # 延迟导入避免循环依赖
        from svc.apps.auth.models import Permission, Role, User
        from svc.apps.auth.repositories import UserRepository
        
        # 创建权限
        permissions = await self._create_permissions(db)
        
        # 创建角色
        roles = await self._create_roles(db, permissions)
        
        # 创建用户
        users = await self._create_users(db, roles)
        
        return {
            "permissions": permissions,
            "roles": roles,
            "users": users
        }
    
    async def _create_permissions(self, db) -> List:
        """创建权限数据"""
        # 实现权限创建逻辑
        return []
    
    async def _create_roles(self, db, permissions) -> List:
        """创建角色数据"""
        # 实现角色创建逻辑
        return []
    
    async def _create_users(self, db, roles) -> List:
        """创建用户数据"""
        # 实现用户创建逻辑
        return []


class ProductModule(SeedDataModule):
    """产品模块种子数据"""
    
    def __init__(self):
        super().__init__("product")
    
    async def create_data(self, db, **kwargs) -> Dict:
        """创建产品相关数据"""
        # 延迟导入避免循环依赖
        from svc.apps.products.repositories.category import CategoryRepository
        from svc.apps.products.repositories.product import ProductRepository
        
        # 创建分类
        categories = await self._create_categories(db)
        
        # 创建产品
        products = await self._create_products(db, categories)
        
        # 创建产品规格
        if kwargs.get('create_specs', True):
            await self._create_product_specs(db, products)
        
        return {
            "categories": categories,
            "products": products
        }
    
    async def _create_categories(self, db) -> List:
        """创建分类数据"""
        from svc.apps.products.repositories.category import CategoryRepository
        from svc.apps.products.schemas.category import CategoryCreate
        
        repo = CategoryRepository(db)
        categories, total = await repo.get_categories()
        
        if total > 0:
            self.logger.info("分类数据已存在，跳过创建")
            return categories
        
        helmet_types = ["安全帽", "骑行盔", "摩托盔", "滑雪盔", "攀岩盔", "马术盔"]
        created = []
        
        for idx, name in enumerate(helmet_types):
            category = await repo.create(CategoryCreate(
                name=name,
                description=f"{name}类头盔产品",
                slug=f"helmet-{idx+1}",
            ).model_dump())
            created.append(category)
            self.logger.info(f"创建分类: {name}")
        
        await db.commit()
        return created
    
    async def _create_products(self, db, categories) -> List:
        """创建产品数据"""
        from svc.apps.products.repositories.product import ProductRepository
        from svc.apps.products.schemas.product import ProductCreate
        
        repo = ProductRepository(db)
        products, total = await repo.get_products()
        
        if total > 0:
            self.logger.info("产品数据已存在，跳过创建")
            return products
        
        # 产品创建逻辑...
        return []
    
    async def _create_product_specs(self, db, products) -> None:
        """创建产品规格数据"""
        # 延迟导入避免循环依赖
        from svc.apps.albums.services.album import AlbumService
        from svc.apps.products.repositories.specs import (
            ProductSpecCombinationRepository, SpecOptionRepository, SpecRepository
        )
        from svc.apps.products.schemas.specs import SpecWithOptions
        from svc.apps.products.services.specs import ProductSpecCombinationService
        
        # 初始化服务
        spec_repo = SpecRepository(db)
        option_repo = SpecOptionRepository(db)
        combination_repo = ProductSpecCombinationRepository(db)
        album_service = AlbumService(db)
        
        spec_service = ProductSpecCombinationService(
            repo=combination_repo,
            album_service=album_service,
            basic_spec_repo=spec_repo,
            spec_option_repo=option_repo
        )
        
        # 定义规格配置
        helmet_specs = [
            SpecWithOptions(
                name="尺寸",
                description="头盔尺寸规格",
                options=["S", "M", "L"],
                sort_order=1
            ),
            SpecWithOptions(
                name="颜色",
                description="头盔颜色规格",
                options=["玄青", "朱砂", "月白", "石青", "黛绿"],
                sort_order=2
            ),
            SpecWithOptions(
                name="材质",
                description="头盔材质规格",
                options=["碳纤维", "ABS"],
                sort_order=3
            )
        ]
        
        # 为每个产品设置规格
        for product in products:
            try:
                # 检查是否已有规格
                existing_result = await spec_service.get_product_specifications(product.id)
                if existing_result.is_success and existing_result.data.combinations:
                    self.logger.info(f"产品 {product.name} 已有规格，跳过")
                    continue
                
                # 创建规格
                result = await spec_service.setup_product_specifications(
                    product_id=product.id,
                    specs_data=helmet_specs,
                    user_id=1
                )
                
                if result.is_success:
                    self.logger.info(f"为产品 {product.name} 创建了 {len(result.data)} 个规格组合")
                else:
                    self.logger.error(f"产品 {product.name} 规格创建失败: {result.message}")
                    
            except Exception as e:
                self.logger.error(f"产品 {product.name} 规格创建异常: {str(e)}")
                continue
        
        await db.commit()


class SeedDataManager:
    """种子数据管理器"""
    
    def __init__(self):
        self.modules = {
            'auth': AuthModule(),
            'product': ProductModule(),
            # 可以继续添加其他模块
        }
    
    async def run_all(self, run_demos: bool = True) -> Dict:
        """运行所有种子数据创建"""
        # 延迟导入数据库会话
        from svc.core.database.session import get_session
        
        results = {}
        
        async with get_session() as db:
            logger.info("开始创建种子数据...")
            
            # 按顺序执行各模块
            for module_name, module in self.modules.items():
                result = await module.run(db, create_specs=(module_name == 'product'))
                results[module_name] = result
                
                if not result['success']:
                    logger.warning(f"模块 {module_name} 创建失败，继续执行其他模块")
            
            # 运行演示功能
            if run_demos:
                await self._run_demos(db, results)
            
            logger.info("种子数据创建完成！")
        
        return results
    
    async def _run_demos(self, db, results) -> None:
        """运行演示功能"""
        if 'product' in results and results['product']['success']:
            products = results['product']['data'].get('products', [])
            if products:
                await self._demo_batch_pricing(db, products)
    
    async def _demo_batch_pricing(self, db, products) -> None:
        """演示批量价格更新"""
        # 延迟导入
        from svc.apps.albums.services.album import AlbumService
        from svc.apps.products.repositories.specs import (
            ProductSpecCombinationRepository, SpecOptionRepository, SpecRepository
        )
        from svc.apps.products.services.specs import ProductSpecCombinationService
        
        logger.info("演示批量价格更新功能...")
        # 演示逻辑...


async def main(recreate: bool = False, reset: bool = False, run_demos: bool = True):
    """主函数"""
    try:
        # 重置数据库
        if recreate or reset:
            DatabaseManager.reset_database(recreate=recreate)
        
        # 创建种子数据
        manager = SeedDataManager()
        results = await manager.run_all(run_demos=run_demos)
        
        # 输出结果摘要
        success_count = sum(1 for r in results.values() if r['success'])
        total_count = len(results)
        
        logger.info(f"种子数据创建完成: {success_count}/{total_count} 个模块成功")
        
        return results
        
    except Exception as e:
        logger.error(f"种子数据创建失败: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="重构后的种子数据脚本")
    parser.add_argument("--recreate", action="store_true", help="重新创建数据库")
    parser.add_argument("--reset", action="store_true", help="重置数据库")
    parser.add_argument("--no-demos", action="store_true", help="跳过演示功能")
    
    args = parser.parse_args()
    
    asyncio.run(main(
        recreate=args.recreate,
        reset=args.reset,
        run_demos=not args.no_demos
    ))
